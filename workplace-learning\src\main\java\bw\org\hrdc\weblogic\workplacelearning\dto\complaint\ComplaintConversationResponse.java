package bw.org.hrdc.weblogic.workplacelearning.dto.complaint;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for complaint conversation response
 * Used to return conversation messages to the client
 * 
 * <AUTHOR>
 * @CreatedOn 17/06/25 15:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintConversationResponse {

    private UUID id;
    private String complaintId;
    private String message;
    private String fromUser;
    private String toUser;
    private String fromRole;
    private String toRole;
    private String messageType;
    private Boolean isRead;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Additional fields for better UI display
    private String fromUserName;
    private String toUserName;
    private Boolean isCurrentUser; // true if the message is from the current logged-in user
}
