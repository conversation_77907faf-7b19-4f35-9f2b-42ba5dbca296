server:
  port: 3452

spring:
  application:
    name: notification
  profiles:
    active: ${PROFILE_ACTIVE:local}
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:ehrdcf_notification}
    username: ${DB_USERNAME:}
    password: ${DB_PASSWORD:}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
      validation-timeout: 5000
  jpa:
    hibernate:
      ddl-auto: update  #'none', 'validate', 'update', 'create', or 'create-drop'
    show-sql: true  # Set to true to log the SQL queries to the console
    liquibase:
      change-log: classpath:db/changelog/db.changelog-master.yaml
  # Security Configuration
  kafka:
    bootstrap-servers: ${KAFKA_SERVER:localhost:9094}

jwt:
  auth:
    converter:
      resource-id: ${KC_CLIENT_ID}
      principal-attribute: principal_username

eureka:
  instance:
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 5   # Reduce interval to avoid expiration issues
    leaseExpirationDurationInSeconds: 10 # Reduce expiration timeout
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: ${EUREKA_DEFAULT_ZONE:http://localhost:8070/eureka/}
    healthcheck:
      enabled: true

management:
  health:
    show-details: always

email:
  smtp:
    server: "smtp.gmail.com"
    port: 465
    username: "<EMAIL>"
    password: "nqjlpwmjqifbcjif"
    auth: true
    ssl: true
    starttls: true

# Logging Configuration
logging:
  level:
    org.springframework.security: INFO
    web: INFO
    org:
      hibernate:
        SQL=DEBUG: DEBUG