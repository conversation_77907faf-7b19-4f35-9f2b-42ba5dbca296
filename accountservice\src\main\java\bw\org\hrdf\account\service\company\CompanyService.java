package bw.org.hrdf.account.service.company;

import bw.org.hrdf.account.dto.CompanyDTO;
import bw.org.hrdf.account.dto.CompanyListDTO;
import bw.org.hrdf.account.dto.CompanyStatisticsDTO;
import bw.org.hrdf.account.dto.EtpListDTO;
import bw.org.hrdf.account.entity.company.*;
import bw.org.hrdf.account.entity.enums.*;
import bw.org.hrdf.account.models.company.*;
import bw.org.hrdf.account.helper.BaseSpecifications;
import bw.org.hrdf.account.helper.CompanyMapper;
import bw.org.hrdf.account.repositories.company.CompanyRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static bw.org.hrdf.account.models.company.VerificationMapper.mapVerificationTypeToActionMessage;
import static bw.org.hrdf.account.models.company.VerificationMapper.mapVerificationTypeToActionType;
import static bw.org.hrdf.account.utils.ReferenceNumberGenerator.generateReferenceNumber;

@Service
public class CompanyService {

    private static final Logger logger = LoggerFactory.getLogger(CompanyService.class);

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private ContactPersonService contactPersonService;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private VerificationService verificationService;

    @PersistenceContext
    private EntityManager entityManager;

    public Map<VerificationTypeEnum, String> getVerificationReferences(List<VerificationModel> verifications) {
        return verifications.stream()
                .collect(Collectors.toMap(
                        VerificationModel::getType,
                        VerificationModel::getReference,
                        (existing, replacement) -> existing
                ));
    }
    public Optional<CompanyEntity> getCompanyByReference(String reference, VerificationTypeEnum type) {
        return companyRepository.findByVerificationReference(reference, type);
    }
    public boolean isCompanyReferenceExists(String reference, VerificationTypeEnum type) {
        return getCompanyByReference(reference, type).isPresent();
    }
    public CompanyEntity buildCompanyEntity(CompanyRequest companyRequest) {
        CompanyEntity company = new CompanyEntity();
        company.setName(companyRequest.getName());
        company.setType(companyRequest.getType());
        company.setIndustry(companyRequest.getIndustry());
        company.setCategory(companyRequest.getCategory());
        company.setPhysicalAddress(companyRequest.getPhysicalAddress());
        company.setTelephoneNumber(companyRequest.getTelephoneNumber());
        company.setFaxNumber(companyRequest.getFaxNumber());
        company.setPepPipStatus(companyRequest.isPepPipStatus());
        company.setPepPipAssociateStatus(companyRequest.isPepPipAssociateStatus());
        return company;
    }
    public CompanyEntity createCompany(CompanyEntity organisation) {
        return companyRepository.save(organisation);
    }
    public void processContactPerson(ContactPersonModel contactPersonModel, CompanyEntity savedCompany) {
        if (contactPersonModel == null) return;

        ContactPerson contactPersonEntity = new ContactPerson(
                contactPersonModel.getName(),
                contactPersonModel.getPosition(),
                contactPersonModel.getMobileNumber(),
                contactPersonModel.getEmail(),
                savedCompany
        );
        if (contactPersonService.save(contactPersonEntity) == null) {
            logger.error("Failed to save contact person for company ID {}", savedCompany.getUuid());
        }
    }
    private StateEnum getApplicationState(CompanyRequest companyRequest, List<VerificationModel> verifications) {
        //TODO enable this when integration is completed
//                if (verifications.stream().anyMatch(v -> !VerificationStatusEnum.VERIFIED.equals(v.getStatus()))) {
//            return StateEnum.DRAFT;
//        }
        return companyRequest.getRegistrationApplication().getState();
    }
    public ApplicationEntity processApplication(CompanyRequest companyRequest, CompanyEntity company, List<VerificationModel> verifications) {
        StateEnum applicationState = getApplicationState(companyRequest, verifications);
        String referenceNumber = generateReferenceNumber("app");

        ApplicationEntity application = new ApplicationEntity(
                referenceNumber,
                StatusEnum.PENDING,
                applicationState,
                company,
                new ArrayList<>(),
                LocalDateTime.now(),
                companyRequest.getRegistrationApplication().getAgentLead(),
                companyRequest.getRegistrationApplication().getAgent(),
                companyRequest.getRegistrationApplication().getManager()
        );

        return applicationService.saveApplication(application);
    }
    private List<Actions> mapPendingActions(List<Verification> verifications, ApplicationEntity application) {
        return verifications.stream()
                .filter(v -> !VerificationStatusEnum.VERIFIED.equals(v.getStatus()))
                .map(v -> new Actions(
                        mapVerificationTypeToActionType(v.getType()),
                        mapVerificationTypeToActionMessage(v.getType()),
                        StatusEnum.PENDING,
                        application
                ))
                .toList();
    }
    public void processVerifications(CompanyEntity company, List<VerificationModel> verifications, ApplicationEntity application) {
        List<Verification> verificationEntities = verifications.stream()
                .map(model -> VerificationMapper.toEntity(model, company))
                .toList();

        if (!verificationEntities.isEmpty()) {
            List<Verification> savedVerifications = verificationService.save(verificationEntities);
            if (savedVerifications != null) {
                List<Actions> pendingActions = mapPendingActions(savedVerifications, application);
                applicationService.createActions(pendingActions);
            }
        }
    }
    public Optional<CompanyDTO> getCompanyByUuid(String uuid) {
        Optional<CompanyEntity> entity = getCompanyById(uuid);
        if(entity.isPresent()){
            return entity.map(CompanyMapper::toDTO);
        }
        return Optional.empty();
    }
    public Optional<CompanyEntity> getCompanyById(String uuid) {
        Specification<CompanyEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("uuid"), uuid));
        return companyRepository.findOne(spec);
    }

    public Page<CompanyListDTO> getAllCompanies(CompanySearchCriteria searchCriteria, PageRequest pageable) {

        Specification<CompanyEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());

        // Handle multiple verification reference checks in one condition
        if (searchCriteria.getBursTin() != null || searchCriteria.getCipaNumber() != null || searchCriteria.getBqaNumber() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> {
                Predicate bursPredicate = searchCriteria.getBursTin() != null
                        ? criteriaBuilder.equal(root.join("verifications").get("reference"), searchCriteria.getBursTin()) : null;
                Predicate cipaPredicate = searchCriteria.getCipaNumber() != null
                        ? criteriaBuilder.equal(root.join("verifications").get("reference"), searchCriteria.getCipaNumber()) : null;
                Predicate bqaPredicate = searchCriteria.getBqaNumber() != null
                        ? criteriaBuilder.equal(root.join("verifications").get("reference"), searchCriteria.getBqaNumber()) : null;

                return criteriaBuilder.or(
                        criteriaBuilder.or(bursPredicate, cipaPredicate),
                        bqaPredicate
                );
            });
        }

        if (searchCriteria.getCompanyName() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("name")), "%" + searchCriteria.getCompanyName().toLowerCase() + "%"));
        }
        if (searchCriteria.getStatus() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.join("registrationApplication").get("status"), StatusEnum.valueOf(searchCriteria.getStatus().toUpperCase())));
        }
        if (searchCriteria.getAssignedTo() != null) {
            if (searchCriteria.getRole() != null && searchCriteria.getRole().equalsIgnoreCase("agent")) {
                spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                        root.join("registrationApplication").get("agent"), searchCriteria.getAssignedTo()));
            } else if (searchCriteria.getRole() != null && searchCriteria.getRole().equalsIgnoreCase("manager")) {
                spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                        root.join("registrationApplication").get("manager"), searchCriteria.getAssignedTo()));
            }
        }
        if (searchCriteria.getType() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("type"), searchCriteria.getType()));
        }
        if (searchCriteria.getIndustry() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("industry")), "%" + searchCriteria.getIndustry().toLowerCase() + "%"));
        }
        if (searchCriteria.getCategory() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("category"), searchCriteria.getCategory()));
        }

        Page<CompanyEntity> entities = companyRepository.findAll(spec, pageable);

        return entities.map(entity -> new CompanyListDTO(
                entity.getUuid(),
                entity.getName(),
                entity.getType().name(),
                entity.getContactPerson() != null ? entity.getContactPerson().getName() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getReferenceNumber() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getStatus().name() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getState().name() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getApplicationSubmissionDate() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getAgentLead() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getAgent() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getManager() : null
        ));
    }
    @Transactional()
    public String updateCompany(CompanyEntity company) {
        try {
            int updated = companyRepository.updateCompanyDetails(
                    company.getUuid().toLowerCase(),
                    company.getName(),
                    company.getType(),
                    company.getIndustry(),
                    company.getCategory(),
                    company.getPhysicalAddress(),
                    company.getTelephoneNumber(),
                    company.getFaxNumber(),
                    company.isPepPipStatus(),
                    company.isPepPipAssociateStatus()
            );
            entityManager.flush();
            if (updated > 0) {
                return "SUCCESSFUL";
            }
            return "FAILED_UPDATE";
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    public String deleteCompany(String id) {
        try {
            int updated = companyRepository.deleteCompany(id);
            if (updated > 0) {
                return "SUCCESSFUL";
            }
            return "FAILED_UPDATE";
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    public String processVerification(Verification verification, ApplicationEntity application) {
        Verification savedVerification = verificationService.save(verification);
        if (savedVerification != null) {
            if(!verification.getStatus().equals(VerificationStatusEnum.VERIFIED)){
                Actions pendingAction = new Actions(
                        mapVerificationTypeToActionType(verification.getType()),
                        mapVerificationTypeToActionMessage(verification.getType()),
                        StatusEnum.PENDING,
                        application
                );
                applicationService.createAction(pendingAction);
            }
            return "SUCCESSFUL";
        }
        return "FAILED";
    }

    public List<CompanyListDTO> getAllNonPageableCompanies() {

        Specification<CompanyEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());

        Set<CompanyEntity> entities = companyRepository.findAllNonPageableCompanies(spec);

        return entities.stream().filter(companyEntity -> !companyEntity.isDeleted()).map(entity -> new CompanyListDTO(
                entity.getUuid(),
                entity.getName(),
                entity.getType().name(),
                entity.getContactPerson() != null ? entity.getContactPerson().getName() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getReferenceNumber() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getStatus().name() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getState().name() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getApplicationSubmissionDate() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getAgentLead() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getAgent() : null,
                entity.getRegistrationApplication() != null ? entity.getRegistrationApplication().getManager() : null
        )).toList();
    }

    public CompanyStatisticsDTO getCompanyStatistics() {
        List<Object[]> results = companyRepository.getCompanyStatistics();
        if (!results.isEmpty()) {
            Object[] row = results.get(0);
            return new CompanyStatisticsDTO(
                    ((Number) row[0]).longValue(),
                    ((Number) row[1]).longValue(),
                    ((Number) row[2]).longValue(),
                    ((Number) row[3]).longValue()
            );
        }
        return new CompanyStatisticsDTO(0L, 0L, 0L, 0L);
    }

    public Page<EtpListDTO> fetchAllActiveEtps(PageRequest pageable) {

        Page<CompanyEntity> entities = companyRepository.fetchAllActiveEtps(pageable);

        return entities.map(entity -> new EtpListDTO(
                entity.getUuid(),
                entity.getName(),
                entity.getType().name()
        ));
    }

    public List<Map<String, String>> searchCompaniesByName(String companyName) {
        if (companyName == null || companyName.trim().isEmpty()) {
            return Collections.emptyList();
        }

        Specification<CompanyEntity> spec = Specification.<CompanyEntity>where(BaseSpecifications.isNotDeleted())
            .and((root, query, criteriaBuilder) -> criteriaBuilder.like(
                criteriaBuilder.lower(root.get("name")), 
                "%" + companyName.toLowerCase().trim() + "%"
            ));

        List<CompanyEntity> companies = companyRepository.findAll(spec);
        
        return companies.stream()
            .map(company -> {
                Map<String, String> result = new HashMap<>();
                result.put("organizationId", company.getUuid());  // Changed from "uuid" to "organizationId"
                result.put("name", company.getName());
                return result;
            })
            .collect(Collectors.toList());
    }
}
