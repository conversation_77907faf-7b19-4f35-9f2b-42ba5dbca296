package bw.org.hrdc.weblogic.workplacelearning.api;

import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import org.springframework.stereotype.Component;

import java.util.ArrayList;


@Component
public class AccountClientFallback implements AccountClient {

 
    @Override
    public ApiResponse<?> fetchAllActiveUsers(String role) {
        return new ApiResponse<>(false, "Account service is currently unavailable for role: " + role, new ArrayList<>(), null);
    }

    
     
    @Override
    public ApiResponse<?> fetchAllUsersByRole(String role) {
        return new ApiResponse<>(false, "Account service is currently unavailable for role: " + role, new ArrayList<>(), null);
    }
}
