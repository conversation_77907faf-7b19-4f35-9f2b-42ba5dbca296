package bw.org.hrdc.weblogic.workplacelearning.api;

import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import org.springframework.stereotype.Component;

import java.util.ArrayList;


@Component
public class AccountClientFallback implements AccountClient {

    /**
     * ✅ CLIENT FEEDBACK: Use existing implementation to get users by roles
     */
    @Override
    public ApiResponse<?> fetchAllActiveUsers(String role) {
        return new ApiResponse<>(false, "Account service is currently unavailable for role: " + role, new ArrayList<>(), null);
    }

    /**
     * ✅ CLIENT FEEDBACK: Agent lead is a user, use existing function to get user by id
     */
    @Override
    public ApiResponse<?> fetchUserById(String id) {
        return new ApiResponse<>(false, "Account service is currently unavailable for user: " + id, null, null);
    }
}
