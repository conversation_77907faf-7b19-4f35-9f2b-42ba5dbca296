2025-06-16T15:38:34.845+05:30  INFO 10228 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 10228 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T15:38:34.858+05:30  INFO 10228 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T15:38:34.963+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-16T15:38:34.964+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-16T15:38:37.424+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T15:38:37.749+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 317 ms. Found 28 JPA repository interfaces.
2025-06-16T15:38:38.260+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T15:38:38.856+05:30  WARN 10228 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:38:38.862+05:30  WARN 10228 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:38:38.869+05:30  WARN 10228 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:38:38.874+05:30  WARN 10228 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:38:38.878+05:30  WARN 10228 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:38:40.007+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T15:38:40.039+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:38:40.040+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T15:38:40.141+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T15:38:40.142+05:30  INFO 10228 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5177 ms
2025-06-16T15:38:40.332+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T15:38:40.847+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@5da3d59b
2025-06-16T15:38:40.852+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T15:38:40.881+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T15:38:41.303+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T15:38:41.373+05:30  INFO 10228 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-16T15:38:41.421+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T15:38:41.742+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T15:38:45.667+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T15:38:48.737+05:30  INFO 10228 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T15:38:50.369+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-16T15:38:56.647+05:30  WARN 10228 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T15:38:56.853+05:30  WARN 10228 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T15:38:56.976+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:38:57.216+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:38:57.255+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:39:00.301+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T15:39:01.756+05:30  INFO 10228 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T15:39:01.830+05:30  WARN 10228 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T15:39:01.964+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T15:39:02.010+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T15:39:02.016+05:30  INFO 10228 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T15:39:02.052+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T15:39:02.053+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T15:39:02.054+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T15:39:02.055+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T15:39:02.055+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T15:39:02.056+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T15:39:02.056+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T15:39:02.450+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T15:39:02.452+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T15:39:02.455+05:30  INFO 10228 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T15:39:02.457+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750068542456 with initial instances count: 3
2025-06-16T15:39:02.469+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T15:39:02.470+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750068542470, current=UP, previous=STARTING]
2025-06-16T15:39:02.471+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T15:39:02.472+05:30  WARN 10228 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T15:39:02.490+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T15:39:02.491+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T15:39:02.530+05:30  INFO 10228 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 28.441 seconds (process running for 29.254)
2025-06-16T15:39:02.537+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T15:44:02.069+05:30  INFO 10228 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T15:48:18.983+05:30  INFO 10228 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 3 class path changes (0 additions, 0 deletions, 3 modifications)
2025-06-16T15:48:18.992+05:30  INFO 10228 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T15:48:18.994+05:30  INFO 10228 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069098994, current=DOWN, previous=UP]
2025-06-16T15:48:19.000+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069099000, current=UP, previous=DOWN]
2025-06-16T15:48:19.001+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T15:48:19.017+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T15:48:19.091+05:30  INFO 10228 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T15:48:19.103+05:30  INFO 10228 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T15:48:19.113+05:30  INFO 10228 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T15:48:19.120+05:30  INFO 10228 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T15:48:22.127+05:30  INFO 10228 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T15:48:22.149+05:30  INFO 10228 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T15:48:22.150+05:30  INFO 10228 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T15:48:22.268+05:30  INFO 10228 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T15:48:22.564+05:30  INFO 10228 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 10228 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T15:48:22.572+05:30  INFO 10228 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T15:48:24.973+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T15:48:25.393+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 418 ms. Found 28 JPA repository interfaces.
2025-06-16T15:48:26.030+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T15:48:26.555+05:30  WARN 10228 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:48:26.583+05:30  WARN 10228 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:48:26.593+05:30  WARN 10228 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:48:26.600+05:30  WARN 10228 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:48:26.613+05:30  WARN 10228 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:48:27.299+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T15:48:27.303+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:48:27.304+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T15:48:27.402+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T15:48:27.403+05:30  INFO 10228 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4823 ms
2025-06-16T15:48:27.717+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T15:48:27.911+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@20a8ce71
2025-06-16T15:48:27.912+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T15:48:27.912+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T15:48:28.624+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T15:48:28.634+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T15:48:28.753+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T15:48:31.203+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T15:48:32.647+05:30  INFO 10228 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T15:48:33.527+05:30  WARN 10228 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T15:48:33.670+05:30  WARN 10228 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T15:48:33.750+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:48:33.901+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:48:33.937+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:48:36.301+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T15:48:37.365+05:30  INFO 10228 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T15:48:37.527+05:30  WARN 10228 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T15:48:37.781+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T15:48:37.796+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T15:48:37.797+05:30  INFO 10228 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T15:48:37.800+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T15:48:37.803+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T15:48:37.804+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T15:48:37.805+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T15:48:37.807+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T15:48:37.807+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T15:48:37.808+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T15:48:37.960+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T15:48:37.962+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T15:48:37.963+05:30  INFO 10228 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T15:48:37.964+05:30  INFO 10228 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750069117964 with initial instances count: 3
2025-06-16T15:48:37.972+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069117971, current=UP, previous=STARTING]
2025-06-16T15:48:37.972+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T15:48:37.989+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T15:48:37.991+05:30  WARN 10228 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T15:48:38.000+05:30  INFO 10228 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T15:48:38.005+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T15:48:38.008+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T15:48:38.059+05:30  INFO 10228 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 15.781 seconds (process running for 604.783)
2025-06-16T15:48:38.070+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T15:49:08.638+05:30  INFO 10228 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T15:49:08.743+05:30  INFO 10228 --- [workplace-learning] [Thread-7] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T15:49:08.747+05:30  INFO 10228 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069148747, current=DOWN, previous=UP]
2025-06-16T15:49:08.758+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069148758, current=UP, previous=DOWN]
2025-06-16T15:49:08.784+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T15:49:08.796+05:30  INFO 10228 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T15:49:08.814+05:30  INFO 10228 --- [workplace-learning] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T15:49:08.842+05:30  INFO 10228 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T15:49:08.878+05:30  INFO 10228 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T15:49:08.893+05:30  INFO 10228 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T15:49:11.909+05:30  INFO 10228 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T15:49:11.931+05:30  INFO 10228 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T15:49:11.932+05:30  INFO 10228 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T15:49:12.125+05:30  INFO 10228 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T15:49:12.436+05:30  INFO 10228 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 10228 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T15:49:12.441+05:30  INFO 10228 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T15:49:14.973+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T15:49:15.358+05:30  INFO 10228 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 352 ms. Found 28 JPA repository interfaces.
2025-06-16T15:51:52.175+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 8760 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T15:51:52.178+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T15:51:52.311+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-16T15:51:52.312+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-16T15:51:56.143+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T15:51:56.868+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 707 ms. Found 28 JPA repository interfaces.
2025-06-16T15:51:57.782+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T15:51:59.350+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:51:59.357+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:51:59.375+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:51:59.387+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:51:59.397+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:52:00.932+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T15:52:00.975+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:52:00.978+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T15:52:01.149+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T15:52:01.150+05:30  INFO 8760 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 8836 ms
2025-06-16T15:52:01.401+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T15:52:01.711+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@3691b672
2025-06-16T15:52:01.722+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T15:52:01.745+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T15:52:02.676+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T15:52:02.983+05:30  INFO 8760 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-16T15:52:03.132+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T15:52:04.377+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T15:52:10.949+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T15:52:12.535+05:30  INFO 8760 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T15:52:13.570+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-16T15:52:17.152+05:30  WARN 8760 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T15:52:17.304+05:30  WARN 8760 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T15:52:17.431+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:52:17.733+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:52:17.796+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:52:21.032+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T15:52:22.421+05:30  INFO 8760 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T15:52:22.517+05:30  WARN 8760 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T15:52:22.629+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T15:52:22.659+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T15:52:22.665+05:30  INFO 8760 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T15:52:22.676+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T15:52:22.676+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T15:52:22.677+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T15:52:22.678+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T15:52:22.678+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T15:52:22.679+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T15:52:22.680+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T15:52:23.038+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T15:52:23.040+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T15:52:23.042+05:30  INFO 8760 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T15:52:23.044+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750069343043 with initial instances count: 3
2025-06-16T15:52:23.055+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T15:52:23.056+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069343056, current=UP, previous=STARTING]
2025-06-16T15:52:23.057+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T15:52:23.057+05:30  WARN 8760 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T15:52:23.075+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T15:52:23.076+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T15:52:23.106+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 31.793 seconds (process running for 33.212)
2025-06-16T15:52:23.113+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T15:53:50.327+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16T15:53:50.328+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-16T15:53:50.331+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-06-16T15:53:50.424+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.c.ComplaintController        : Complaint create initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-06-16T15:53:50.426+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-06-16T15:53:51.052+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-06-16T15:53:51.053+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-06-16T15:53:51.161+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {agent-001-uuid-1234-5678-9012=1, b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, agent-lead-001-uuid-1234-5678-9012=1, ad9f2ba3-898c-487f-a40e-893a10fe8477=2}
2025-06-16T15:53:51.162+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent 9dbc9c30-d927-47c3-b826-d46ddc42e2cd with 0 active complaints
2025-06-16T15:53:51.162+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: 9dbc9c30-d927-47c3-b826-d46ddc42e2cd
2025-06-16T15:53:51.408+05:30  WARN 8760 --- [workplace-learning] [http-nio-8091-exec-1] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-16T15:53:51.409+05:30  WARN 8760 --- [workplace-learning] [http-nio-8091-exec-1] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-16T15:54:05.447+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.c.ComplaintController        : Client response recorded for complaint id: 6ac4c129-c24f-454d-9f54-055f6a189948
2025-06-16T15:54:05.463+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Updating complaint: 6ac4c129-c24f-454d-9f54-055f6a189948
2025-06-16T15:54:05.464+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Current Status: IN_PROGRESS, Current State: ESCALATED
2025-06-16T15:54:05.464+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Requested Status: IN_PROGRESS, Requested State: ESCALATED
2025-06-16T15:54:05.464+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Validating transition from IN_PROGRESS:ESCALATED to IN_PROGRESS:ESCALATED
2025-06-16T15:54:05.465+05:30  WARN 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Workflow transition validation failed: Invalid status/state transition - Proceeding anyway for testing
2025-06-16T15:54:05.465+05:30  WARN 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Current: IN_PROGRESS:ESCALATED, Requested: IN_PROGRESS:ESCALATED
2025-06-16T15:54:05.466+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status updated to: IN_PROGRESS, state updated to: ESCALATED
2025-06-16T15:54:05.474+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Audit log created for status change
2025-06-16T15:54:05.507+05:30 ERROR 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch user details for agent: b493bcc6-551d-4554-aa68-2a1d15cc1d0e, error: [404] during [GET] to [http://account-service/api/v1/user/backoffice/user/b493bcc6-551d-4554-aa68-2a1d15cc1d0e] [AccountClient#fetchUserById(String)]: [{"timestamp":"2025-06-16T10:24:05.499+00:00","status":404,"error":"Not Found","path":"/api/v1/user/backoffice/user/b493bcc6-551d-4554-aa68-2a1d15cc1d0e"}]
2025-06-16T15:54:05.507+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Using fallback role: AGENT
2025-06-16T15:54:05.508+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Determined action type: Agent_action for role: AGENT
2025-06-16T15:54:05.508+05:30  WARN 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : No process instance ID found for complaint 6ac4c129-c24f-454d-9f54-055f6a189948
2025-06-16T15:54:05.509+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status update completed successfully for complaint: 6ac4c129-c24f-454d-9f54-055f6a189948
2025-06-16T15:54:12.873+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.c.ComplaintController        : Client response recorded for complaint id: 6ac4c129-c24f-454d-9f54-055f6a189948
2025-06-16T15:54:12.878+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Updating complaint: 6ac4c129-c24f-454d-9f54-055f6a189948
2025-06-16T15:54:12.879+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Current Status: IN_PROGRESS, Current State: ESCALATED
2025-06-16T15:54:12.879+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Requested Status: IN_PROGRESS, Requested State: ESCALATED
2025-06-16T15:54:12.880+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Validating transition from IN_PROGRESS:ESCALATED to IN_PROGRESS:ESCALATED
2025-06-16T15:54:12.880+05:30  WARN 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Workflow transition validation failed: Invalid status/state transition - Proceeding anyway for testing
2025-06-16T15:54:12.881+05:30  WARN 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Current: IN_PROGRESS:ESCALATED, Requested: IN_PROGRESS:ESCALATED
2025-06-16T15:54:12.881+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status updated to: IN_PROGRESS, state updated to: ESCALATED
2025-06-16T15:54:12.887+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Audit log created for status change
2025-06-16T15:54:12.898+05:30 ERROR 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Failed to fetch user details for agent: b493bcc6-551d-4554-aa68-2a1d15cc1d0e, error: [404] during [GET] to [http://account-service/api/v1/user/backoffice/user/b493bcc6-551d-4554-aa68-2a1d15cc1d0e] [AccountClient#fetchUserById(String)]: [{"timestamp":"2025-06-16T10:24:12.895+00:00","status":404,"error":"Not Found","path":"/api/v1/user/backoffice/user/b493bcc6-551d-4554-aa68-2a1d15cc1d0e"}]
2025-06-16T15:54:12.899+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Using fallback role: AGENT
2025-06-16T15:54:12.900+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Determined action type: Agent_action for role: AGENT
2025-06-16T15:54:12.900+05:30  WARN 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : No process instance ID found for complaint 6ac4c129-c24f-454d-9f54-055f6a189948
2025-06-16T15:54:12.900+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status update completed successfully for complaint: 6ac4c129-c24f-454d-9f54-055f6a189948
2025-06-16T15:54:28.641+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.c.c.ComplaintController        : Fetching complaints with criteria - role: AGENT, userId: null, companyId: null, status: null, state: null, assignedTo: null, search: null
2025-06-16T15:54:28.642+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.c.c.ComplaintController        : Role-based filtering: role=AGENT, userId=null
2025-06-16T15:54:28.983+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT and userId: null
2025-06-16T15:54:28.983+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.s.complaint.ComplaintService   : AGENT filter (without userId): state=SUBMITTED (all submitted complaints)
2025-06-16T15:54:45.653+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.c.ComplaintController        : Fetching complaints with criteria - role: AGENT_LEAD, userId: null, companyId: null, status: null, state: null, assignedTo: null, search: null
2025-06-16T15:54:45.654+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.c.ComplaintController        : Role-based filtering: role=AGENT_LEAD, userId=null
2025-06-16T15:54:45.779+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT_LEAD and userId: null
2025-06-16T15:54:45.780+05:30  INFO 8760 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : AGENT_LEAD filter (without userId): state=ESCALATED (all escalated complaints)
2025-06-16T15:57:22.684+05:30  INFO 8760 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T15:58:16.700+05:30  INFO 8760 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T15:58:16.711+05:30  INFO 8760 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T15:58:16.713+05:30  INFO 8760 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069696713, current=DOWN, previous=UP]
2025-06-16T15:58:16.721+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069696721, current=UP, previous=DOWN]
2025-06-16T15:58:16.764+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T15:58:16.801+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T15:58:16.888+05:30  INFO 8760 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T15:58:16.897+05:30  INFO 8760 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T15:58:16.912+05:30  INFO 8760 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T15:58:16.929+05:30  INFO 8760 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T15:58:19.947+05:30  INFO 8760 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T15:58:19.966+05:30  INFO 8760 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T15:58:19.967+05:30  INFO 8760 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T15:58:20.149+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T15:58:20.333+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 8760 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T15:58:20.334+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T15:58:22.957+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T15:58:23.277+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 317 ms. Found 28 JPA repository interfaces.
2025-06-16T15:58:23.847+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T15:58:24.116+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:58:24.122+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:58:24.134+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:58:24.140+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:58:24.148+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:58:24.519+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T15:58:24.522+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:58:24.522+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T15:58:24.618+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T15:58:24.619+05:30  INFO 8760 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4277 ms
2025-06-16T15:58:24.904+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T15:58:25.142+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@79a18974
2025-06-16T15:58:25.143+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T15:58:25.145+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T15:58:25.562+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T15:58:25.575+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T15:58:25.653+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T15:58:27.546+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T15:58:28.379+05:30  INFO 8760 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T15:58:29.099+05:30  WARN 8760 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T15:58:29.164+05:30  WARN 8760 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T15:58:29.224+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:58:29.363+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:58:29.393+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:58:31.663+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T15:58:32.817+05:30  INFO 8760 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T15:58:32.875+05:30  WARN 8760 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T15:58:32.934+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T15:58:32.941+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T15:58:32.941+05:30  INFO 8760 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T15:58:32.942+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T15:58:32.943+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T15:58:32.943+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T15:58:32.943+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T15:58:32.944+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T15:58:32.944+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T15:58:32.944+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T15:58:33.056+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T15:58:33.058+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T15:58:33.059+05:30  INFO 8760 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T15:58:33.060+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750069713060 with initial instances count: 3
2025-06-16T15:58:33.065+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069713065, current=UP, previous=STARTING]
2025-06-16T15:58:33.067+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T15:58:33.084+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T15:58:33.087+05:30  WARN 8760 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T15:58:33.093+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T15:58:33.096+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T15:58:33.098+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T15:58:33.134+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 12.948 seconds (process running for 403.24)
2025-06-16T15:58:33.141+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T15:58:57.407+05:30  INFO 8760 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T15:58:57.436+05:30  INFO 8760 --- [workplace-learning] [Thread-7] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T15:58:57.442+05:30  INFO 8760 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069737442, current=DOWN, previous=UP]
2025-06-16T15:58:57.472+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069737472, current=UP, previous=DOWN]
2025-06-16T15:58:57.497+05:30  INFO 8760 --- [workplace-learning] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T15:58:57.504+05:30  WARN 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T15:58:57.507+05:30  INFO 8760 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T15:58:57.512+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T15:58:57.572+05:30  INFO 8760 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T15:58:57.600+05:30  INFO 8760 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T15:58:57.602+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T15:58:57.625+05:30  INFO 8760 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T15:58:57.671+05:30  INFO 8760 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T15:58:57.677+05:30  INFO 8760 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T15:58:57.855+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T15:58:58.119+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 8760 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T15:58:58.121+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T15:59:00.241+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T15:59:00.537+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 294 ms. Found 28 JPA repository interfaces.
2025-06-16T15:59:01.239+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T15:59:01.490+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:59:01.495+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:59:01.500+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:59:01.505+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:59:01.509+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:59:01.782+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T15:59:01.783+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:59:01.784+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T15:59:01.851+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T15:59:01.851+05:30  INFO 8760 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3724 ms
2025-06-16T15:59:02.003+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T15:59:02.116+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@2ede6d90
2025-06-16T15:59:02.117+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T15:59:02.117+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T15:59:02.544+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T15:59:02.550+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T15:59:02.574+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T15:59:04.767+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T15:59:05.961+05:30  INFO 8760 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T15:59:07.283+05:30  WARN 8760 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T15:59:07.390+05:30  WARN 8760 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T15:59:07.468+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:59:07.665+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:59:07.710+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T15:59:10.022+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T15:59:11.355+05:30  INFO 8760 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T15:59:11.766+05:30  WARN 8760 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T15:59:12.011+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T15:59:12.036+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T15:59:12.040+05:30  INFO 8760 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T15:59:12.042+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T15:59:12.047+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T15:59:12.052+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T15:59:12.059+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T15:59:12.061+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T15:59:12.066+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T15:59:12.068+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T15:59:12.202+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T15:59:12.203+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T15:59:12.205+05:30  INFO 8760 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T15:59:12.206+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750069752206 with initial instances count: 3
2025-06-16T15:59:12.212+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069752212, current=UP, previous=STARTING]
2025-06-16T15:59:12.214+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T15:59:12.248+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T15:59:12.250+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T15:59:12.256+05:30  WARN 8760 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T15:59:12.273+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T15:59:12.276+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T15:59:12.362+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 14.48 seconds (process running for 442.469)
2025-06-16T15:59:12.380+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T15:59:42.653+05:30  INFO 8760 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T15:59:43.123+05:30  INFO 8760 --- [workplace-learning] [Thread-13] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T15:59:43.217+05:30  INFO 8760 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069783217, current=DOWN, previous=UP]
2025-06-16T15:59:43.466+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069783466, current=UP, previous=DOWN]
2025-06-16T15:59:43.493+05:30  INFO 8760 --- [workplace-learning] [Thread-13] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T15:59:43.723+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T15:59:43.806+05:30  INFO 8760 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T15:59:44.066+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T15:59:44.195+05:30  INFO 8760 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T15:59:44.256+05:30  INFO 8760 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T15:59:47.438+05:30  INFO 8760 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T15:59:47.459+05:30  INFO 8760 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T15:59:47.463+05:30  INFO 8760 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T15:59:47.842+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T15:59:48.551+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 8760 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T15:59:48.585+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T15:59:51.651+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T15:59:52.126+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 451 ms. Found 28 JPA repository interfaces.
2025-06-16T15:59:52.982+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T15:59:53.393+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:59:53.425+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:59:53.457+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T15:59:53.499+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:59:53.553+05:30  WARN 8760 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T15:59:54.154+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T15:59:54.207+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T15:59:54.254+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T15:59:54.406+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T15:59:54.426+05:30  INFO 8760 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 5823 ms
2025-06-16T15:59:54.809+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T15:59:55.500+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@47ce8497
2025-06-16T15:59:55.504+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T15:59:55.508+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T15:59:56.017+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T15:59:56.037+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T15:59:56.094+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T15:59:58.314+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T15:59:59.709+05:30  INFO 8760 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:00:01.087+05:30  WARN 8760 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T16:00:01.392+05:30  WARN 8760 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T16:00:01.552+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:00:01.834+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:00:01.901+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:00:05.384+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T16:00:07.114+05:30  INFO 8760 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T16:00:07.300+05:30  WARN 8760 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T16:00:07.444+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T16:00:07.452+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T16:00:07.453+05:30  INFO 8760 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:00:07.453+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T16:00:07.454+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T16:00:07.454+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T16:00:07.454+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T16:00:07.455+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T16:00:07.455+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T16:00:07.455+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T16:00:07.530+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T16:00:07.531+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T16:00:07.532+05:30  INFO 8760 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T16:00:07.532+05:30  INFO 8760 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750069807532 with initial instances count: 3
2025-06-16T16:00:07.535+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069807535, current=UP, previous=STARTING]
2025-06-16T16:00:07.535+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:00:07.550+05:30  INFO 8760 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:00:07.552+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T16:00:07.553+05:30  WARN 8760 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:00:07.557+05:30  INFO 8760 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T16:00:07.558+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T16:00:07.583+05:30  INFO 8760 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 19.671 seconds (process running for 497.688)
2025-06-16T16:00:07.587+05:30  INFO 8760 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T16:01:51.189+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 22484 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T16:01:51.192+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T16:01:51.332+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-16T16:01:51.373+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-16T16:01:53.292+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T16:01:53.579+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 279 ms. Found 28 JPA repository interfaces.
2025-06-16T16:01:53.954+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T16:01:54.582+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:01:54.587+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:01:54.596+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:01:54.600+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:01:54.604+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:01:55.208+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T16:01:55.232+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T16:01:55.233+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T16:01:55.318+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T16:01:55.319+05:30  INFO 22484 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3907 ms
2025-06-16T16:01:55.486+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T16:01:55.702+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@5b1edb45
2025-06-16T16:01:55.704+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T16:01:55.715+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T16:01:56.049+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T16:01:56.153+05:30  INFO 22484 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-16T16:01:56.212+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T16:01:56.689+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T16:02:00.493+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T16:02:01.465+05:30  INFO 22484 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:02:02.023+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-16T16:02:03.793+05:30  WARN 22484 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T16:02:03.880+05:30  WARN 22484 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T16:02:03.957+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:02:04.143+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:02:04.177+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:02:08.370+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T16:02:10.218+05:30  INFO 22484 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T16:02:10.320+05:30  WARN 22484 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T16:02:10.470+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T16:02:10.508+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T16:02:10.516+05:30  INFO 22484 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:02:10.529+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T16:02:10.530+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T16:02:10.531+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T16:02:10.531+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T16:02:10.532+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T16:02:10.532+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T16:02:10.533+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T16:02:11.138+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T16:02:11.142+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T16:02:11.145+05:30  INFO 22484 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T16:02:11.148+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750069931147 with initial instances count: 3
2025-06-16T16:02:11.165+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T16:02:11.167+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750069931167, current=UP, previous=STARTING]
2025-06-16T16:02:11.168+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:02:11.169+05:30  WARN 22484 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:02:11.196+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T16:02:11.199+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T16:02:11.249+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 20.654 seconds (process running for 21.381)
2025-06-16T16:02:11.252+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:04:42.348+05:30  INFO 22484 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T16:04:42.352+05:30  INFO 22484 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T16:04:42.353+05:30  INFO 22484 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070082353, current=DOWN, previous=UP]
2025-06-16T16:04:42.358+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070082358, current=UP, previous=DOWN]
2025-06-16T16:04:42.362+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:04:42.372+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:04:42.406+05:30  INFO 22484 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:04:42.413+05:30  INFO 22484 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T16:04:42.424+05:30  INFO 22484 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T16:04:42.425+05:30  INFO 22484 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T16:04:45.440+05:30  INFO 22484 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T16:04:45.459+05:30  INFO 22484 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T16:04:45.459+05:30  INFO 22484 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T16:04:45.568+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T16:04:45.684+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 22484 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T16:04:45.685+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T16:04:50.507+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T16:04:50.781+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 272 ms. Found 28 JPA repository interfaces.
2025-06-16T16:04:51.195+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T16:04:51.464+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:04:51.471+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:04:51.483+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:04:51.492+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:04:51.497+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:04:51.921+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T16:04:51.926+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T16:04:51.927+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T16:04:52.040+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T16:04:52.041+05:30  INFO 22484 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 6351 ms
2025-06-16T16:04:52.308+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T16:04:52.498+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@78fdddf6
2025-06-16T16:04:52.499+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T16:04:52.500+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T16:04:53.134+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T16:04:53.147+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T16:04:53.195+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T16:04:55.830+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T16:04:57.104+05:30  INFO 22484 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:04:58.331+05:30  WARN 22484 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T16:04:58.541+05:30  WARN 22484 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T16:04:58.670+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:04:58.930+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:04:58.976+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:05:01.511+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T16:05:03.002+05:30  INFO 22484 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T16:05:03.101+05:30  WARN 22484 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T16:05:03.232+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T16:05:03.242+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T16:05:03.243+05:30  INFO 22484 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:05:03.244+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T16:05:03.244+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T16:05:03.245+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T16:05:03.245+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T16:05:03.246+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T16:05:03.246+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T16:05:03.247+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T16:05:03.336+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T16:05:03.337+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T16:05:03.338+05:30  INFO 22484 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T16:05:03.339+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750070103339 with initial instances count: 3
2025-06-16T16:05:03.344+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070103344, current=UP, previous=STARTING]
2025-06-16T16:05:03.346+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:05:03.359+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T16:05:03.362+05:30  WARN 22484 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:05:03.367+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:05:03.370+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T16:05:03.372+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T16:05:03.411+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 17.831 seconds (process running for 193.543)
2025-06-16T16:05:03.419+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T16:05:07.400+05:30  INFO 22484 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T16:05:07.418+05:30  INFO 22484 --- [workplace-learning] [Thread-7] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T16:05:07.422+05:30  INFO 22484 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070107422, current=DOWN, previous=UP]
2025-06-16T16:05:07.424+05:30  WARN 22484 --- [workplace-learning] [Thread-7] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:05:07.460+05:30  INFO 22484 --- [workplace-learning] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:05:07.464+05:30  INFO 22484 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T16:05:07.472+05:30  INFO 22484 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T16:05:07.579+05:30  INFO 22484 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T16:05:08.466+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - Re-registering apps/WORKPLACE-LEARNING
2025-06-16T16:05:08.467+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:05:08.471+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:05:10.615+05:30  INFO 22484 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T16:05:10.656+05:30  INFO 22484 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T16:05:10.657+05:30  INFO 22484 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T16:05:10.813+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T16:05:10.935+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 22484 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T16:05:10.937+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T16:05:12.263+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T16:05:12.564+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 297 ms. Found 28 JPA repository interfaces.
2025-06-16T16:05:13.037+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T16:05:13.234+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:05:13.240+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:05:13.248+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:05:13.253+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:05:13.257+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:05:13.555+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T16:05:13.557+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T16:05:13.558+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T16:05:13.628+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T16:05:13.629+05:30  INFO 22484 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2687 ms
2025-06-16T16:05:13.919+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T16:05:14.166+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@fed6402
2025-06-16T16:05:14.167+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T16:05:14.167+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T16:05:14.426+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T16:05:14.433+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T16:05:14.460+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T16:05:15.652+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T16:05:16.364+05:30  INFO 22484 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:05:17.209+05:30  WARN 22484 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T16:05:17.304+05:30  WARN 22484 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T16:05:17.368+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:05:17.799+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:05:17.882+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:05:21.395+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T16:05:22.768+05:30  INFO 22484 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T16:05:22.861+05:30  WARN 22484 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T16:05:23.007+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T16:05:23.027+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T16:05:23.028+05:30  INFO 22484 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:05:23.029+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T16:05:23.029+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T16:05:23.029+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T16:05:23.030+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T16:05:23.030+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T16:05:23.031+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T16:05:23.031+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T16:05:23.110+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T16:05:23.111+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T16:05:23.112+05:30  INFO 22484 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T16:05:23.113+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750070123113 with initial instances count: 3
2025-06-16T16:05:23.117+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070123117, current=UP, previous=STARTING]
2025-06-16T16:05:23.117+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:05:23.132+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T16:05:23.133+05:30  WARN 22484 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:05:23.139+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:05:23.142+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T16:05:23.145+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T16:05:23.181+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 12.354 seconds (process running for 213.313)
2025-06-16T16:05:23.190+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T16:06:41.695+05:30  INFO 22484 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T16:06:41.698+05:30  INFO 22484 --- [workplace-learning] [Thread-13] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T16:06:41.699+05:30  INFO 22484 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070201699, current=DOWN, previous=UP]
2025-06-16T16:06:41.705+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070201705, current=UP, previous=DOWN]
2025-06-16T16:06:41.707+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:06:41.721+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:06:41.744+05:30  INFO 22484 --- [workplace-learning] [Thread-13] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:06:41.759+05:30  INFO 22484 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T16:06:41.782+05:30  INFO 22484 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T16:06:41.790+05:30  INFO 22484 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T16:06:44.802+05:30  INFO 22484 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T16:06:44.824+05:30  INFO 22484 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T16:06:44.825+05:30  INFO 22484 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T16:06:45.006+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T16:06:45.234+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 22484 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T16:06:45.254+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T16:06:47.777+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T16:06:48.049+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 270 ms. Found 28 JPA repository interfaces.
2025-06-16T16:06:48.595+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T16:06:48.875+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:06:48.882+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:06:48.896+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:06:48.908+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:06:48.919+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:06:49.369+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T16:06:49.372+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T16:06:49.373+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T16:06:49.723+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T16:06:49.725+05:30  INFO 22484 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4424 ms
2025-06-16T16:06:49.897+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T16:06:50.027+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@46aab091
2025-06-16T16:06:50.028+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T16:06:50.028+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T16:06:50.425+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T16:06:50.433+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T16:06:50.464+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T16:06:52.522+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T16:06:53.637+05:30  INFO 22484 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:06:54.906+05:30  WARN 22484 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T16:06:54.991+05:30  WARN 22484 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T16:06:55.050+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:06:55.233+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:06:55.276+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:06:58.738+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T16:07:01.617+05:30  INFO 22484 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T16:07:01.739+05:30  WARN 22484 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T16:07:01.934+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T16:07:01.950+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T16:07:01.953+05:30  INFO 22484 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:07:01.955+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T16:07:01.957+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T16:07:01.959+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T16:07:01.960+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T16:07:01.961+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T16:07:01.962+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T16:07:01.965+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T16:07:02.115+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T16:07:02.119+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T16:07:02.121+05:30  INFO 22484 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T16:07:02.123+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750070222123 with initial instances count: 3
2025-06-16T16:07:02.161+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070222161, current=UP, previous=STARTING]
2025-06-16T16:07:02.162+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:07:02.164+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T16:07:02.194+05:30  WARN 22484 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:07:02.207+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T16:07:02.210+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:07:02.232+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T16:07:02.299+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 17.27 seconds (process running for 312.43)
2025-06-16T16:07:02.312+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T16:07:03.416+05:30  INFO 22484 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T16:07:03.418+05:30  INFO 22484 --- [workplace-learning] [Thread-19] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T16:07:03.420+05:30  INFO 22484 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070223420, current=DOWN, previous=UP]
2025-06-16T16:07:03.421+05:30  WARN 22484 --- [workplace-learning] [Thread-19] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:07:03.477+05:30  INFO 22484 --- [workplace-learning] [Thread-19] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:07:03.483+05:30  INFO 22484 --- [workplace-learning] [Thread-19] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T16:07:03.496+05:30  INFO 22484 --- [workplace-learning] [Thread-19] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T16:07:03.501+05:30  INFO 22484 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T16:07:06.517+05:30  INFO 22484 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T16:07:06.533+05:30  INFO 22484 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T16:07:06.537+05:30  INFO 22484 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T16:07:06.744+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T16:07:06.979+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 22484 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T16:07:06.986+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T16:07:10.149+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T16:07:10.463+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 312 ms. Found 28 JPA repository interfaces.
2025-06-16T16:07:11.091+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T16:07:11.356+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:07:11.360+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:07:11.366+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:07:11.374+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:07:11.377+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:07:11.689+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T16:07:11.690+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T16:07:11.691+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T16:07:11.748+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T16:07:11.749+05:30  INFO 22484 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4737 ms
2025-06-16T16:07:11.895+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T16:07:12.150+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@208dfb8c
2025-06-16T16:07:12.153+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T16:07:12.154+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T16:07:12.665+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T16:07:12.677+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T16:07:12.717+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T16:07:14.422+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T16:07:15.763+05:30  INFO 22484 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:07:18.050+05:30  WARN 22484 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T16:07:18.282+05:30  WARN 22484 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T16:07:18.461+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:07:19.266+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:07:19.351+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:07:23.622+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T16:07:24.806+05:30  INFO 22484 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T16:07:24.878+05:30  WARN 22484 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T16:07:24.960+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T16:07:24.967+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T16:07:24.967+05:30  INFO 22484 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:07:24.968+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T16:07:24.969+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T16:07:24.969+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T16:07:24.969+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T16:07:24.970+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T16:07:24.970+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T16:07:24.971+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T16:07:25.037+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T16:07:25.039+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T16:07:25.040+05:30  INFO 22484 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T16:07:25.041+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750070245041 with initial instances count: 3
2025-06-16T16:07:25.046+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070245046, current=UP, previous=STARTING]
2025-06-16T16:07:25.046+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:07:25.063+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T16:07:25.064+05:30  INFO 22484 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:07:25.066+05:30  WARN 22484 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:07:25.074+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T16:07:25.077+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T16:07:25.129+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 18.363 seconds (process running for 335.26)
2025-06-16T16:07:25.137+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T16:07:26.491+05:30  INFO 22484 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T16:07:26.494+05:30  INFO 22484 --- [workplace-learning] [Thread-25] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T16:07:26.494+05:30  INFO 22484 --- [workplace-learning] [Thread-25] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070246494, current=DOWN, previous=UP]
2025-06-16T16:07:26.495+05:30  WARN 22484 --- [workplace-learning] [Thread-25] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:07:26.529+05:30  INFO 22484 --- [workplace-learning] [Thread-25] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:07:26.531+05:30  INFO 22484 --- [workplace-learning] [Thread-25] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T16:07:26.536+05:30  INFO 22484 --- [workplace-learning] [Thread-25] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T16:07:26.538+05:30  INFO 22484 --- [workplace-learning] [Thread-25] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T16:07:29.547+05:30  INFO 22484 --- [workplace-learning] [Thread-25] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T16:07:29.565+05:30  INFO 22484 --- [workplace-learning] [Thread-25] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T16:07:29.565+05:30  INFO 22484 --- [workplace-learning] [Thread-25] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T16:07:29.678+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T16:07:29.718+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 22484 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T16:07:29.719+05:30  INFO 22484 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T16:07:30.380+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T16:07:30.513+05:30  INFO 22484 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 131 ms. Found 28 JPA repository interfaces.
2025-06-16T16:07:30.680+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T16:07:30.779+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:07:30.784+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:07:30.790+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:07:30.794+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:07:30.796+05:30  WARN 22484 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:07:30.946+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T16:07:30.947+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T16:07:30.948+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T16:07:30.991+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T16:07:30.992+05:30  INFO 22484 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1270 ms
2025-06-16T16:07:31.103+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T16:07:31.195+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@3a6b60e5
2025-06-16T16:07:31.195+05:30  INFO 22484 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T16:07:31.196+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T16:07:31.394+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T16:07:31.397+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T16:07:31.414+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T16:07:33.670+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T16:07:35.945+05:30  INFO 22484 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:07:38.217+05:30  WARN 22484 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T16:07:38.447+05:30  WARN 22484 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T16:07:38.641+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:07:39.118+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:07:39.222+05:30  INFO 22484 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:08:41.516+05:30  INFO 14604 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 14604 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T16:08:41.522+05:30  INFO 14604 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T16:08:41.716+05:30  INFO 14604 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-16T16:08:41.717+05:30  INFO 14604 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-16T16:08:45.373+05:30  INFO 14604 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T16:08:46.031+05:30  INFO 14604 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 642 ms. Found 28 JPA repository interfaces.
2025-06-16T16:08:46.934+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T16:08:48.110+05:30  WARN 14604 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:08:48.123+05:30  WARN 14604 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:08:48.139+05:30  WARN 14604 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:08:48.148+05:30  WARN 14604 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:08:48.157+05:30  WARN 14604 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:08:49.717+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T16:08:49.749+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T16:08:49.751+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T16:08:49.932+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T16:08:49.933+05:30  INFO 14604 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 8213 ms
2025-06-16T16:08:50.317+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T16:08:50.736+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@7686fa79
2025-06-16T16:08:50.740+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T16:08:50.767+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T16:08:51.449+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T16:08:51.615+05:30  INFO 14604 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-16T16:08:51.707+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T16:08:52.514+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T16:08:56.950+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T16:08:58.115+05:30  INFO 14604 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:08:58.795+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-16T16:09:01.463+05:30  WARN 14604 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T16:09:01.580+05:30  WARN 14604 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T16:09:01.690+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:09:01.933+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:09:01.982+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:09:07.035+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T16:09:09.054+05:30  INFO 14604 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T16:09:09.139+05:30  WARN 14604 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T16:09:09.256+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T16:09:09.304+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T16:09:09.310+05:30  INFO 14604 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:09:09.322+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T16:09:09.322+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T16:09:09.323+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T16:09:09.324+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T16:09:09.324+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T16:09:09.325+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T16:09:09.325+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T16:09:09.756+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T16:09:09.760+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T16:09:09.763+05:30  INFO 14604 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T16:09:09.766+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750070349765 with initial instances count: 3
2025-06-16T16:09:09.784+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T16:09:09.784+05:30  INFO 14604 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070349784, current=UP, previous=STARTING]
2025-06-16T16:09:09.785+05:30  INFO 14604 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:09:09.786+05:30  WARN 14604 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:09:09.810+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T16:09:09.812+05:30  INFO 14604 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T16:09:09.850+05:30  INFO 14604 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:09:09.851+05:30  INFO 14604 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 30.122 seconds (process running for 31.857)
2025-06-16T16:09:55.735+05:30  INFO 14604 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T16:09:55.754+05:30  INFO 14604 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T16:09:55.756+05:30  INFO 14604 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070395756, current=DOWN, previous=UP]
2025-06-16T16:09:55.803+05:30  INFO 14604 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070395803, current=UP, previous=DOWN]
2025-06-16T16:09:55.839+05:30  INFO 14604 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:09:56.003+05:30  INFO 14604 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:09:56.096+05:30  INFO 14604 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:09:56.142+05:30  INFO 14604 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T16:09:56.487+05:30  INFO 14604 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T16:09:56.576+05:30  INFO 14604 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T16:09:59.637+05:30  INFO 14604 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T16:09:59.704+05:30  INFO 14604 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T16:09:59.709+05:30  INFO 14604 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T16:09:59.902+05:30  INFO 14604 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T16:10:00.307+05:30  INFO 14604 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 14604 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T16:10:00.309+05:30  INFO 14604 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T16:10:03.237+05:30  INFO 14604 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T16:10:03.635+05:30  INFO 14604 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 394 ms. Found 28 JPA repository interfaces.
2025-06-16T16:10:04.157+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T16:10:04.454+05:30  WARN 14604 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:10:04.461+05:30  WARN 14604 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:10:04.470+05:30  WARN 14604 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:10:04.477+05:30  WARN 14604 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:10:04.483+05:30  WARN 14604 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:10:04.905+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T16:10:04.907+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T16:10:04.909+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T16:10:05.018+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T16:10:05.019+05:30  INFO 14604 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4696 ms
2025-06-16T16:10:05.311+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T16:10:05.517+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@19988985
2025-06-16T16:10:05.518+05:30  INFO 14604 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T16:10:05.519+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T16:10:05.956+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T16:10:05.963+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T16:10:06.042+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T16:10:08.496+05:30  INFO 14604 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T16:10:29.687+05:30  INFO 11992 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 11992 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T16:10:29.690+05:30  INFO 11992 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T16:10:29.816+05:30  INFO 11992 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-16T16:10:29.816+05:30  INFO 11992 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-16T16:10:32.075+05:30  INFO 11992 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T16:10:32.430+05:30  INFO 11992 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 345 ms. Found 28 JPA repository interfaces.
2025-06-16T16:10:33.087+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T16:10:33.813+05:30  WARN 11992 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:10:33.819+05:30  WARN 11992 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:10:33.830+05:30  WARN 11992 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:10:33.836+05:30  WARN 11992 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:10:33.840+05:30  WARN 11992 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:10:34.455+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T16:10:34.473+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T16:10:34.473+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T16:10:34.560+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T16:10:34.561+05:30  INFO 11992 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4742 ms
2025-06-16T16:10:34.758+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T16:10:35.015+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@467ae444
2025-06-16T16:10:35.017+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T16:10:35.033+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T16:10:35.405+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T16:10:35.486+05:30  INFO 11992 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-16T16:10:35.538+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T16:10:35.908+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T16:10:38.432+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T16:10:39.090+05:30  INFO 11992 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:10:39.480+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-16T16:10:40.752+05:30  WARN 11992 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T16:10:40.824+05:30  WARN 11992 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T16:10:40.899+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:10:41.025+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:10:41.058+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:10:43.593+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T16:10:44.704+05:30  INFO 11992 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T16:10:44.763+05:30  WARN 11992 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T16:10:44.847+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T16:10:44.873+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T16:10:44.881+05:30  INFO 11992 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:10:44.881+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T16:10:44.881+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T16:10:44.881+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T16:10:44.881+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T16:10:44.881+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T16:10:44.881+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T16:10:44.881+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T16:10:45.175+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T16:10:45.190+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T16:10:45.190+05:30  INFO 11992 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T16:10:45.190+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750070445190 with initial instances count: 3
2025-06-16T16:10:45.197+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T16:10:45.197+05:30  INFO 11992 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750070445197, current=UP, previous=STARTING]
2025-06-16T16:10:45.207+05:30  INFO 11992 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:10:45.207+05:30  WARN 11992 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:10:45.221+05:30  INFO 11992 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T16:10:45.223+05:30  INFO 11992 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T16:10:45.244+05:30  INFO 11992 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 16.829 seconds (process running for 18.655)
2025-06-16T16:10:45.253+05:30  INFO 11992 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:11:43.968+05:30  INFO 11992 --- [workplace-learning] [http-nio-8091-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16T16:11:43.969+05:30  INFO 11992 --- [workplace-learning] [http-nio-8091-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-16T16:11:43.974+05:30  INFO 11992 --- [workplace-learning] [http-nio-8091-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 5 ms
2025-06-16T16:11:44.073+05:30  INFO 11992 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.c.ComplaintController        : Complaint create initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-06-16T16:11:44.075+05:30  INFO 11992 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-06-16T16:11:47.501+05:30  INFO 11992 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-06-16T16:11:47.502+05:30  INFO 11992 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-06-16T16:11:47.623+05:30  INFO 11992 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {agent-001-uuid-1234-5678-9012=1, b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, agent-lead-001-uuid-1234-5678-9012=1, 9dbc9c30-d927-47c3-b826-d46ddc42e2cd=1, ad9f2ba3-898c-487f-a40e-893a10fe8477=2}
2025-06-16T16:11:47.624+05:30  INFO 11992 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent 33bb3982-6234-401d-be81-ae20f1160b88 with 0 active complaints
2025-06-16T16:11:47.624+05:30  INFO 11992 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: 33bb3982-6234-401d-be81-ae20f1160b88
2025-06-16T16:11:47.878+05:30  WARN 11992 --- [workplace-learning] [http-nio-8091-exec-2] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-16T16:11:47.880+05:30  WARN 11992 --- [workplace-learning] [http-nio-8091-exec-2] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-16T16:15:44.925+05:30  INFO 11992 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:20:44.958+05:30  INFO 11992 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:25:44.966+05:30  INFO 11992 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:29:31.972+05:30  INFO 11992 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] o.a.h.c.h.i.c.HttpRequestRetryExec       : Recoverable I/O exception (java.net.SocketException) caught when processing request to {}->http://localhost:8070
2025-06-16T16:29:32.048+05:30  INFO 11992 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/} exception=I/O error on PUT request for "http://localhost:8070/eureka/apps/WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: getsockopt stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on PUT request for "http://localhost:8070/eureka/apps/WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: getsockopt
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.sendHeartBeat(RestTemplateEurekaHttpClient.java:99)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:91)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:845)
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1402)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.socket.PlainConnectionSocketFactory.lambda$connectSocket$0(PlainConnectionSocketFactory.java:91)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at org.apache.hc.client5.http.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:90)
	at org.apache.hc.client5.http.socket.ConnectionSocketFactory.connectSocket(ConnectionSocketFactory.java:123)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:189)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:450)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:162)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:172)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:142)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:152)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:116)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:170)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:112)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:170)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 19 more

2025-06-16T16:29:32.174+05:30  WARN 11992 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on PUT request for "http://localhost:8070/eureka/apps/WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: getsockopt
2025-06-16T16:29:32.355+05:30  INFO 11992 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] c.n.d.s.t.d.RedirectingEurekaHttpClient  : Request execution error. endpoint=DefaultEndpoint{ serviceUrl='http://localhost:8070/eureka/}, exception=I/O error on PUT request for "http://localhost:8070/eureka/apps/WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: getsockopt stacktrace=org.springframework.web.client.ResourceAccessException: I/O error on PUT request for "http://localhost:8070/eureka/apps/WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: getsockopt
	at org.springframework.web.client.RestTemplate.createResourceAccessException(RestTemplate.java:915)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:895)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:790)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:672)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateEurekaHttpClient.sendHeartBeat(RestTemplateEurekaHttpClient.java:99)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:121)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:80)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89)
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:845)
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1402)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.apache.hc.client5.http.HttpHostConnectException: Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: getsockopt
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:682)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:592)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:751)
	at org.apache.hc.client5.http.socket.PlainConnectionSocketFactory.lambda$connectSocket$0(PlainConnectionSocketFactory.java:91)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:571)
	at org.apache.hc.client5.http.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:90)
	at org.apache.hc.client5.http.socket.ConnectionSocketFactory.connectSocket(ConnectionSocketFactory.java:123)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:189)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:450)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:162)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:172)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:142)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:113)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:152)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:116)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:170)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:87)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:55)
	at org.apache.hc.client5.http.classic.HttpClient.executeOpen(HttpClient.java:183)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:99)
	at org.springframework.http.client.AbstractStreamingClientHttpRequest.executeInternal(AbstractStreamingClientHttpRequest.java:70)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:112)
	at org.springframework.cloud.netflix.eureka.http.RestTemplateTransportClientFactory.lambda$restTemplate$3(RestTemplateTransportClientFactory.java:170)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:88)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:72)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:889)
	... 20 more

2025-06-16T16:29:32.968+05:30  WARN 11992 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] c.n.d.s.t.d.RetryableEurekaHttpClient    : Request execution failed with message: I/O error on PUT request for "http://localhost:8070/eureka/apps/WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091": Connect to http://localhost:8070 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: getsockopt
2025-06-16T16:29:33.146+05:30 ERROR 11992 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - was unable to send heartbeat!

com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112) ~[eureka-client-2.0.3.jar:2.0.3]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-2.0.3.jar:2.0.3]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$3.execute(EurekaHttpClientDecorator.java:92) ~[eureka-client-2.0.3.jar:2.0.3]
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:76) ~[eureka-client-2.0.3.jar:2.0.3]
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.sendHeartBeat(EurekaHttpClientDecorator.java:89) ~[eureka-client-2.0.3.jar:2.0.3]
	at com.netflix.discovery.DiscoveryClient.renew(DiscoveryClient.java:845) ~[eureka-client-2.0.3.jar:2.0.3]
	at com.netflix.discovery.DiscoveryClient$HeartbeatThread.run(DiscoveryClient.java:1402) ~[eureka-client-2.0.3.jar:2.0.3]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]

2025-06-16T16:53:35.983+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 9620 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T16:53:35.986+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T16:53:36.113+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-16T16:53:36.113+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-16T16:53:38.408+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T16:53:38.758+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 342 ms. Found 28 JPA repository interfaces.
2025-06-16T16:53:39.194+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T16:53:39.730+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:53:39.735+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:53:39.744+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T16:53:39.749+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:53:39.754+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T16:53:40.352+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T16:53:40.374+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T16:53:40.375+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T16:53:40.493+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T16:53:40.494+05:30  INFO 9620 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4379 ms
2025-06-16T16:53:40.694+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T16:53:40.937+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@2d9f772b
2025-06-16T16:53:40.939+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T16:53:40.951+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T16:53:41.555+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T16:53:41.683+05:30  INFO 9620 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-16T16:53:41.742+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T16:53:42.128+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T16:53:46.166+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T16:53:46.993+05:30  INFO 9620 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T16:53:47.440+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-16T16:53:49.040+05:30  WARN 9620 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T16:53:49.112+05:30  WARN 9620 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T16:53:49.204+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:53:49.393+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:53:49.414+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T16:53:52.318+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T16:53:55.013+05:30  INFO 9620 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T16:53:55.801+05:30  WARN 9620 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T16:53:56.757+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T16:53:56.933+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T16:53:56.966+05:30  INFO 9620 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T16:53:57.029+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T16:53:57.031+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T16:53:57.032+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T16:53:57.036+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T16:53:57.042+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T16:53:57.050+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T16:53:57.053+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T16:54:00.128+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T16:54:00.146+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T16:54:00.169+05:30  INFO 9620 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T16:54:00.384+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750073040362 with initial instances count: 3
2025-06-16T16:54:00.634+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750073040634, current=UP, previous=STARTING]
2025-06-16T16:54:00.656+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T16:54:00.743+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T16:54:00.832+05:30  WARN 9620 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T16:54:00.943+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T16:54:00.974+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T16:54:01.121+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T16:54:01.293+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 26.286 seconds (process running for 27.687)
2025-06-16T16:58:57.074+05:30  INFO 9620 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T17:03:20.862+05:30  INFO 9620 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T17:03:20.876+05:30  INFO 9620 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T17:03:20.883+05:30  INFO 9620 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750073600883, current=DOWN, previous=UP]
2025-06-16T17:03:20.895+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750073600895, current=UP, previous=DOWN]
2025-06-16T17:03:20.895+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:03:20.925+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:03:20.977+05:30  INFO 9620 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:03:20.989+05:30  INFO 9620 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T17:03:21.004+05:30  INFO 9620 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T17:03:21.012+05:30  INFO 9620 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T17:03:24.025+05:30  INFO 9620 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T17:03:24.463+05:30  INFO 9620 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T17:03:24.469+05:30  INFO 9620 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T17:03:24.810+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T17:03:25.452+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 9620 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T17:03:25.458+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T17:03:37.520+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T17:03:38.302+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 779 ms. Found 28 JPA repository interfaces.
2025-06-16T17:03:39.948+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T17:03:40.805+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:03:40.824+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:03:40.861+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:03:40.889+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:03:40.907+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:03:43.039+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T17:03:43.052+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T17:03:43.061+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T17:03:43.400+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T17:03:43.402+05:30  INFO 9620 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 17918 ms
2025-06-16T17:03:44.329+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T17:03:44.968+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@4157042e
2025-06-16T17:03:45.001+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T17:03:45.070+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T17:03:47.010+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T17:03:47.079+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T17:03:47.421+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T17:03:54.313+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T17:03:55.516+05:30  INFO 9620 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:03:56.917+05:30  WARN 9620 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T17:03:57.045+05:30  WARN 9620 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T17:03:57.126+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:03:57.300+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:03:57.351+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:04:01.242+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T17:04:03.349+05:30  INFO 9620 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T17:04:03.607+05:30  WARN 9620 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T17:04:03.949+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T17:04:03.994+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T17:04:04.019+05:30  INFO 9620 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T17:04:04.025+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T17:04:04.041+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T17:04:04.104+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T17:04:04.165+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T17:04:04.171+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T17:04:04.174+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T17:04:04.175+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T17:04:04.374+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T17:04:04.376+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T17:04:04.380+05:30  INFO 9620 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T17:04:04.382+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750073644382 with initial instances count: 3
2025-06-16T17:04:04.387+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750073644387, current=UP, previous=STARTING]
2025-06-16T17:04:04.388+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:04:04.413+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T17:04:04.416+05:30  WARN 9620 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:04:04.416+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:04:04.424+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T17:04:04.426+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T17:04:04.467+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 39.607 seconds (process running for 630.861)
2025-06-16T17:04:04.475+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T17:04:05.073+05:30  INFO 9620 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T17:04:05.075+05:30  INFO 9620 --- [workplace-learning] [Thread-7] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T17:04:05.076+05:30  INFO 9620 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750073645076, current=DOWN, previous=UP]
2025-06-16T17:04:05.077+05:30  WARN 9620 --- [workplace-learning] [Thread-7] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:04:05.105+05:30  INFO 9620 --- [workplace-learning] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:04:05.108+05:30  INFO 9620 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T17:04:05.113+05:30  INFO 9620 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T17:04:05.117+05:30  INFO 9620 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T17:04:08.131+05:30  INFO 9620 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T17:04:08.169+05:30  INFO 9620 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T17:04:08.172+05:30  INFO 9620 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T17:04:08.729+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T17:04:09.926+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 9620 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T17:04:09.932+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T17:04:20.685+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T17:04:21.909+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1174 ms. Found 28 JPA repository interfaces.
2025-06-16T17:04:22.798+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T17:04:23.485+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:04:23.495+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:04:23.542+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:04:23.555+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:04:23.562+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:04:24.492+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T17:04:24.522+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T17:04:24.526+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T17:04:24.768+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T17:04:24.779+05:30  INFO 9620 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 14830 ms
2025-06-16T17:04:25.367+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T17:04:25.573+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@6c69111e
2025-06-16T17:04:25.575+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T17:04:25.575+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T17:04:26.225+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T17:04:26.242+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T17:04:26.300+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T17:04:29.081+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T17:04:30.579+05:30  INFO 9620 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:04:32.418+05:30  WARN 9620 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T17:04:32.789+05:30  WARN 9620 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T17:04:33.200+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:04:33.675+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:04:33.854+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:04:37.088+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T17:04:38.001+05:30  INFO 9620 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T17:04:38.062+05:30  WARN 9620 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T17:04:38.154+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T17:04:38.160+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T17:04:38.160+05:30  INFO 9620 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T17:04:38.161+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T17:04:38.162+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T17:04:38.162+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T17:04:38.163+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T17:04:38.163+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T17:04:38.164+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T17:04:38.164+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T17:04:38.222+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T17:04:38.222+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T17:04:38.223+05:30  INFO 9620 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T17:04:38.224+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750073678224 with initial instances count: 3
2025-06-16T17:04:38.228+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750073678228, current=UP, previous=STARTING]
2025-06-16T17:04:38.228+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:04:38.242+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T17:04:38.243+05:30  WARN 9620 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:04:38.245+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:04:38.249+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T17:04:38.251+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T17:04:38.280+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 29.287 seconds (process running for 664.674)
2025-06-16T17:04:38.285+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T17:04:39.541+05:30  INFO 9620 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T17:04:39.542+05:30  INFO 9620 --- [workplace-learning] [Thread-13] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T17:04:39.543+05:30  INFO 9620 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750073679543, current=DOWN, previous=UP]
2025-06-16T17:04:39.543+05:30  WARN 9620 --- [workplace-learning] [Thread-13] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:04:39.557+05:30  INFO 9620 --- [workplace-learning] [Thread-13] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:04:39.558+05:30  INFO 9620 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T17:04:39.561+05:30  INFO 9620 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T17:04:39.562+05:30  INFO 9620 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T17:04:42.576+05:30  INFO 9620 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T17:04:42.602+05:30  INFO 9620 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T17:04:42.604+05:30  INFO 9620 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T17:04:42.966+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T17:04:43.237+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 9620 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T17:04:43.240+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T17:04:45.323+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T17:04:45.548+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 223 ms. Found 28 JPA repository interfaces.
2025-06-16T17:04:45.754+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T17:04:45.909+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:04:45.913+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:04:45.917+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:04:45.923+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:04:45.927+05:30  WARN 9620 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:04:46.202+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T17:04:46.203+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T17:04:46.203+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T17:04:46.257+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T17:04:46.257+05:30  INFO 9620 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3000 ms
2025-06-16T17:04:46.397+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T17:04:46.499+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@1b4de978
2025-06-16T17:04:46.499+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T17:04:46.500+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T17:04:46.716+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T17:04:46.721+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T17:04:46.743+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T17:04:47.977+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T17:04:48.666+05:30  INFO 9620 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:04:49.500+05:30  WARN 9620 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T17:04:49.574+05:30  WARN 9620 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T17:04:49.632+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:04:49.825+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:04:49.868+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:04:51.589+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T17:04:52.345+05:30  INFO 9620 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T17:04:52.405+05:30  WARN 9620 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T17:04:52.454+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T17:04:52.458+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T17:04:52.459+05:30  INFO 9620 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T17:04:52.459+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T17:04:52.460+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T17:04:52.460+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T17:04:52.461+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T17:04:52.461+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T17:04:52.461+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T17:04:52.462+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T17:04:52.507+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T17:04:52.508+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T17:04:52.509+05:30  INFO 9620 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T17:04:52.510+05:30  INFO 9620 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750073692510 with initial instances count: 3
2025-06-16T17:04:52.513+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750073692513, current=UP, previous=STARTING]
2025-06-16T17:04:52.514+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:04:52.521+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T17:04:52.523+05:30  WARN 9620 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:04:52.528+05:30  INFO 9620 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T17:04:52.529+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T17:04:52.561+05:30  INFO 9620 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 9.566 seconds (process running for 678.955)
2025-06-16T17:04:52.568+05:30  INFO 9620 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T17:04:52.647+05:30  INFO 9620 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:37:12.842+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 24176 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T17:37:12.842+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T17:37:12.953+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-16T17:37:12.953+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-16T17:37:14.964+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T17:37:15.309+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 330 ms. Found 28 JPA repository interfaces.
2025-06-16T17:37:15.710+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T17:37:16.295+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:37:16.295+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:37:16.311+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:37:16.313+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:37:16.313+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:37:16.871+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T17:37:16.886+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T17:37:16.886+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T17:37:16.975+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T17:37:16.975+05:30  INFO 24176 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4020 ms
2025-06-16T17:37:17.156+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T17:37:17.374+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@6f18b239
2025-06-16T17:37:17.374+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T17:37:17.374+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T17:37:17.732+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T17:37:17.828+05:30  INFO 24176 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-16T17:37:17.901+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T17:37:18.308+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T17:37:21.139+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T17:37:21.981+05:30  INFO 24176 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:37:22.453+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-16T17:37:24.181+05:30  WARN 24176 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T17:37:24.262+05:30  WARN 24176 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T17:37:24.337+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:37:24.492+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:37:24.525+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:37:27.472+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T17:37:28.616+05:30  INFO 24176 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T17:37:28.688+05:30  WARN 24176 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T17:37:28.789+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T17:37:28.824+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T17:37:28.824+05:30  INFO 24176 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T17:37:28.842+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T17:37:28.842+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T17:37:28.842+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T17:37:28.842+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T17:37:28.842+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T17:37:28.842+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T17:37:28.842+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T17:37:29.192+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T17:37:29.192+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T17:37:29.192+05:30  INFO 24176 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T17:37:29.209+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750075649209 with initial instances count: 3
2025-06-16T17:37:29.227+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T17:37:29.227+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750075649227, current=UP, previous=STARTING]
2025-06-16T17:37:29.228+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:37:29.228+05:30  WARN 24176 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:37:29.234+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T17:37:29.249+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T17:37:29.272+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 17.13 seconds (process running for 17.968)
2025-06-16T17:37:29.272+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:39:00.362+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16T17:39:00.362+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-16T17:39:00.379+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 17 ms
2025-06-16T17:39:00.480+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.c.ComplaintController        : Complaint create initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-06-16T17:39:00.480+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-06-16T17:39:01.129+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-06-16T17:39:01.130+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-06-16T17:39:01.231+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {33bb3982-6234-401d-be81-ae20f1160b88=1, agent-001-uuid-1234-5678-9012=1, b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, agent-lead-001-uuid-1234-5678-9012=1, 9dbc9c30-d927-47c3-b826-d46ddc42e2cd=1, ad9f2ba3-898c-487f-a40e-893a10fe8477=2}
2025-06-16T17:39:01.231+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent d1ee8398-e5e6-49e9-be1e-896b4e8cf289 with 0 active complaints
2025-06-16T17:39:01.231+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: d1ee8398-e5e6-49e9-be1e-896b4e8cf289
2025-06-16T17:39:01.485+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-1] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-16T17:39:01.485+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-1] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-16T17:39:21.565+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.c.c.ComplaintController        : Complaint create initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-06-16T17:39:21.566+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch agents from Account Service for load balancing...
2025-06-16T17:39:22.108+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Account Service response - Status: true, Data: [{id=ad9f2ba3-898c-487f-a40e-893a10fe8477, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=33bb3982-6234-401d-be81-ae20f1160b88, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=d1ee8398-e5e6-49e9-be1e-896b4e8cf289, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-06-16T17:39:22.109+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Found 4 agents from Account Service
2025-06-16T17:39:22.128+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Current agent workloads: {33bb3982-6234-401d-be81-ae20f1160b88=1, d1ee8398-e5e6-49e9-be1e-896b4e8cf289=1, agent-001-uuid-1234-5678-9012=1, b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, agent-lead-001-uuid-1234-5678-9012=1, 9dbc9c30-d927-47c3-b826-d46ddc42e2cd=1, ad9f2ba3-898c-487f-a40e-893a10fe8477=2}
2025-06-16T17:39:22.128+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent 9dbc9c30-d927-47c3-b826-d46ddc42e2cd with 1 active complaints
2025-06-16T17:39:22.129+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-4] b.o.h.w.w.s.complaint.ComplaintService   : Assigned complaint to agent with least workload: 9dbc9c30-d927-47c3-b826-d46ddc42e2cd
2025-06-16T17:39:22.155+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-4] o.s.c.l.core.RoundRobinLoadBalancer      : No servers available for service: WORKFLOW
2025-06-16T17:39:22.156+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-4] RetryableFeignBlockingLoadBalancerClient : Service instance was not resolved, executing the original request
2025-06-16T17:39:49.782+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.c.ComplaintController        : Client response recorded for complaint id: 9dbc9c30-d927-47c3-b826-d46ddc42e2cd
2025-06-16T17:39:49.806+05:30 ERROR 24176 --- [workplace-learning] [http-nio-8091-exec-3] b.o.h.w.w.c.c.ComplaintController        : Failed to record client response for complaint id 9dbc9c30-d927-47c3-b826-d46ddc42e2cd with exception: Complaint not found
2025-06-16T17:40:16.754+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.c.c.ComplaintController        : Client response recorded for complaint id: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:40:16.760+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Updating complaint: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:40:16.770+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Current Status: OPEN, Current State: SUBMITTED
2025-06-16T17:40:16.770+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Requested Status: IN_PROGRESS, Requested State: UNDER_REVIEW
2025-06-16T17:40:16.770+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Validating transition from OPEN:SUBMITTED to IN_PROGRESS:UNDER_REVIEW
2025-06-16T17:40:16.771+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Workflow transition validation passed
2025-06-16T17:40:16.771+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status updated to: IN_PROGRESS, state updated to: UNDER_REVIEW
2025-06-16T17:40:16.797+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Audit log created for status change
2025-06-16T17:40:16.797+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Looking up user 9dbc9c30-d927-47c3-b826-d46ddc42e2cd with role: agent
2025-06-16T17:40:17.377+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Found user details: {id=9dbc9c30-d927-47c3-b826-d46ddc42e2cd, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}
2025-06-16T17:40:17.377+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Determined role from user lookup: AGENT
2025-06-16T17:40:17.377+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Determined action type: Agent_action for role: AGENT
2025-06-16T17:40:17.377+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : No process instance ID found for complaint ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:40:17.377+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status update completed successfully for complaint: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:10.480+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.c.ComplaintController        : Complaint escalation initiated for complaint id: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:10.486+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch Agent Leads from Account Service...
2025-06-16T17:41:11.077+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Agent Leads response - Status: true, Data: [{id=b493bcc6-551d-4554-aa68-2a1d15cc1d0e, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=6bdc67ca-188b-4ba4-b176-ad6943194adc, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=dea76361-eb99-4fb6-902e-0f11b369d3eb, email=<EMAIL>, enabled=true, firstName=Faith, lastName=Seitshiro, username=<EMAIL>}, {id=c1f814f2-6107-4d4c-a57c-8f2b269215a3, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=f3daeda0-463e-4d8d-8c29-e6852b34cd00, email=<EMAIL>, enabled=true, firstName=Agent, lastName=Lead, username=<EMAIL>}, {id=e3f74d1f-761d-4ef1-93ff-d7c168d257b3, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=fa9dd5de-45d3-4dbd-aca6-895bd5f8a098, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=669bbfcc-179d-4f97-aace-0745a1b04c4c, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=3514191d-6d0d-4e9c-8fe7-d186fd324d73, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-06-16T17:41:11.080+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Found 9 agent leads from Account Service
2025-06-16T17:41:11.111+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Current agent lead workloads: {b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, agent-lead-001-uuid-1234-5678-9012=1}
2025-06-16T17:41:11.114+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent lead 6bdc67ca-188b-4ba4-b176-ad6943194adc with 0 active escalated complaints
2025-06-16T17:41:11.115+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Assigned escalated complaint to Agent Lead with least workload: 6bdc67ca-188b-4ba4-b176-ad6943194adc
2025-06-16T17:41:11.121+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Updating complaint: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:11.123+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Current Status: IN_PROGRESS, Current State: UNDER_REVIEW
2025-06-16T17:41:11.123+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Requested Status: OPEN, Requested State: ESCALATED
2025-06-16T17:41:11.124+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Validating transition from IN_PROGRESS:UNDER_REVIEW to OPEN:ESCALATED
2025-06-16T17:41:11.125+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Workflow transition validation passed
2025-06-16T17:41:11.126+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status updated to: OPEN, state updated to: ESCALATED
2025-06-16T17:41:11.172+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Audit log created for status change
2025-06-16T17:41:11.173+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Looking up user 6bdc67ca-188b-4ba4-b176-ad6943194adc with role: agent_lead
2025-06-16T17:41:11.675+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Found user details: {id=6bdc67ca-188b-4ba4-b176-ad6943194adc, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}
2025-06-16T17:41:11.676+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Determined role from user lookup: AGENT_LEAD
2025-06-16T17:41:11.677+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Determined action type: null for role: AGENT_LEAD
2025-06-16T17:41:11.678+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : No process instance ID found for complaint ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:11.678+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : No action type determined for complaint ff7f9449-6705-428e-bfda-c82b4c0dcd0c - role: AGENT_LEAD
2025-06-16T17:41:11.679+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status update completed successfully for complaint: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:11.680+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Notification should be sent to Agent Lead: 6bdc67ca-188b-4ba4-b176-ad6943194adc
2025-06-16T17:41:11.681+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Complaint ff7f9449-6705-428e-bfda-c82b4c0dcd0c escalated to Agent Lead: 6bdc67ca-188b-4ba4-b176-ad6943194adc
2025-06-16T17:41:20.523+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.c.c.ComplaintController        : Complaint closure initiated for complaint id: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:20.533+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Updating complaint: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:20.534+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Current Status: OPEN, Current State: ESCALATED
2025-06-16T17:41:20.535+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Requested Status: CLOSED, Requested State: COMPLETED
2025-06-16T17:41:20.536+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Validating transition from OPEN:ESCALATED to CLOSED:COMPLETED
2025-06-16T17:41:20.536+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Workflow transition validation passed
2025-06-16T17:41:20.537+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status updated to: CLOSED, state updated to: COMPLETED
2025-06-16T17:41:20.553+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Audit log created for status change
2025-06-16T17:41:20.554+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Looking up user 6bdc67ca-188b-4ba4-b176-ad6943194adc with role: agent
2025-06-16T17:41:21.062+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : User 6bdc67ca-188b-4ba4-b176-ad6943194adc not found in agent role
2025-06-16T17:41:21.064+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Determined action type: Agent_action for role: AGENT
2025-06-16T17:41:21.065+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : No process instance ID found for complaint ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:21.066+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status update completed successfully for complaint: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:21.066+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Notification should be sent to complaint creator about closure
2025-06-16T17:41:21.067+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-8] b.o.h.w.w.s.complaint.ComplaintService   : Complaint ff7f9449-6705-428e-bfda-c82b4c0dcd0c closed successfully
2025-06-16T17:41:29.957+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-9] b.o.h.w.w.c.c.ComplaintController        : Fetching complaints with criteria - role: AGENT_LEAD, userId: null, companyId: null, status: null, state: null, assignedTo: null, search: null
2025-06-16T17:41:29.958+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-9] b.o.h.w.w.c.c.ComplaintController        : Role-based filtering: role=AGENT_LEAD, userId=null
2025-06-16T17:41:30.419+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-9] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT_LEAD and userId: null
2025-06-16T17:41:30.419+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-9] b.o.h.w.w.s.complaint.ComplaintService   : AGENT_LEAD filter (without userId): state=ESCALATED (all escalated complaints)
2025-06-16T17:41:36.360+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.c.ComplaintController        : Fetching complaints with criteria - role: AGENT_LEAD, userId: null, companyId: null, status: null, state: null, assignedTo: null, search: null
2025-06-16T17:41:36.360+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.c.ComplaintController        : Role-based filtering: role=AGENT_LEAD, userId=null
2025-06-16T17:41:36.484+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT_LEAD and userId: null
2025-06-16T17:41:36.485+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.s.complaint.ComplaintService   : AGENT_LEAD filter (without userId): state=ESCALATED (all escalated complaints)
2025-06-16T17:41:57.736+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.c.ComplaintController        : Complaint escalation initiated for complaint id: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:57.742+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Attempting to fetch Agent Leads from Account Service...
2025-06-16T17:41:58.227+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Agent Leads response - Status: true, Data: [{id=b493bcc6-551d-4554-aa68-2a1d15cc1d0e, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=6bdc67ca-188b-4ba4-b176-ad6943194adc, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=dea76361-eb99-4fb6-902e-0f11b369d3eb, email=<EMAIL>, enabled=true, firstName=Faith, lastName=Seitshiro, username=<EMAIL>}, {id=c1f814f2-6107-4d4c-a57c-8f2b269215a3, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=f3daeda0-463e-4d8d-8c29-e6852b34cd00, email=<EMAIL>, enabled=true, firstName=Agent, lastName=Lead, username=<EMAIL>}, {id=e3f74d1f-761d-4ef1-93ff-d7c168d257b3, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=fa9dd5de-45d3-4dbd-aca6-895bd5f8a098, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=669bbfcc-179d-4f97-aace-0745a1b04c4c, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=3514191d-6d0d-4e9c-8fe7-d186fd324d73, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-06-16T17:41:58.227+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Found 9 agent leads from Account Service
2025-06-16T17:41:58.248+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Current agent lead workloads: {b493bcc6-551d-4554-aa68-2a1d15cc1d0e=1, agent-lead-001-uuid-1234-5678-9012=1}
2025-06-16T17:41:58.248+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Selected agent lead 6bdc67ca-188b-4ba4-b176-ad6943194adc with 0 active escalated complaints
2025-06-16T17:41:58.248+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Assigned escalated complaint to Agent Lead with least workload: 6bdc67ca-188b-4ba4-b176-ad6943194adc
2025-06-16T17:41:58.248+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Updating complaint: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:58.248+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Current Status: CLOSED, Current State: COMPLETED
2025-06-16T17:41:58.248+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Requested Status: OPEN, Requested State: ESCALATED
2025-06-16T17:41:58.260+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Validating transition from CLOSED:COMPLETED to OPEN:ESCALATED
2025-06-16T17:41:58.260+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Workflow transition validation failed: Invalid status/state transition - Proceeding anyway for testing
2025-06-16T17:41:58.262+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Current: CLOSED:COMPLETED, Requested: OPEN:ESCALATED
2025-06-16T17:41:58.262+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status updated to: OPEN, state updated to: ESCALATED
2025-06-16T17:41:58.262+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Audit log created for status change
2025-06-16T17:41:58.262+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Looking up user 6bdc67ca-188b-4ba4-b176-ad6943194adc with role: agent_lead
2025-06-16T17:41:58.778+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Found user details: {id=6bdc67ca-188b-4ba4-b176-ad6943194adc, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}
2025-06-16T17:41:58.778+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Determined role from user lookup: AGENT_LEAD
2025-06-16T17:41:58.778+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Determined action type: null for role: AGENT_LEAD
2025-06-16T17:41:58.778+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : No process instance ID found for complaint ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:58.778+05:30  WARN 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : No action type determined for complaint ff7f9449-6705-428e-bfda-c82b4c0dcd0c - role: AGENT_LEAD
2025-06-16T17:41:58.778+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Complaint status update completed successfully for complaint: ff7f9449-6705-428e-bfda-c82b4c0dcd0c
2025-06-16T17:41:58.778+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Notification should be sent to Agent Lead: 6bdc67ca-188b-4ba4-b176-ad6943194adc
2025-06-16T17:41:58.778+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Complaint ff7f9449-6705-428e-bfda-c82b4c0dcd0c escalated to Agent Lead: 6bdc67ca-188b-4ba4-b176-ad6943194adc
2025-06-16T17:42:06.255+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.c.ComplaintController        : Fetching complaints with criteria - role: AGENT_LEAD, userId: null, companyId: null, status: null, state: null, assignedTo: null, search: null
2025-06-16T17:42:06.255+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.c.c.ComplaintController        : Role-based filtering: role=AGENT_LEAD, userId=null
2025-06-16T17:42:06.365+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT_LEAD and userId: null
2025-06-16T17:42:06.373+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-1] b.o.h.w.w.s.complaint.ComplaintService   : AGENT_LEAD filter (without userId): state=ESCALATED (all escalated complaints)
2025-06-16T17:42:28.864+05:30  INFO 24176 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T17:43:30.419+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.c.c.ComplaintController        : Fetching complaints with criteria - role: AGENT, userId: null, companyId: null, status: null, state: null, assignedTo: null, search: null
2025-06-16T17:43:30.419+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.c.c.ComplaintController        : Role-based filtering: role=AGENT, userId=null
2025-06-16T17:43:30.577+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT and userId: null
2025-06-16T17:43:30.577+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.s.complaint.ComplaintService   : AGENT filter (without userId): state=SUBMITTED (all submitted complaints)
2025-06-16T17:43:30.585+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT and userId: null
2025-06-16T17:43:30.585+05:30  INFO 24176 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.s.complaint.ComplaintService   : AGENT filter (without userId): state=SUBMITTED (all submitted complaints)
2025-06-16T17:44:20.845+05:30  INFO 24176 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T17:44:20.855+05:30  INFO 24176 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T17:44:20.857+05:30  INFO 24176 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750076060857, current=DOWN, previous=UP]
2025-06-16T17:44:20.878+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750076060877, current=UP, previous=DOWN]
2025-06-16T17:44:20.880+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:44:20.903+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:44:20.977+05:30  INFO 24176 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:44:20.989+05:30  INFO 24176 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T17:44:21.013+05:30  INFO 24176 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T17:44:21.022+05:30  INFO 24176 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T17:44:24.034+05:30  INFO 24176 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T17:44:24.053+05:30  INFO 24176 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T17:44:24.054+05:30  INFO 24176 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T17:44:24.267+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T17:44:24.576+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 24176 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T17:44:24.579+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T17:44:27.558+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T17:44:28.076+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 516 ms. Found 28 JPA repository interfaces.
2025-06-16T17:44:28.480+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T17:44:28.748+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:44:28.753+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:44:28.759+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:44:28.764+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:44:28.770+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:44:29.154+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T17:44:29.159+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T17:44:29.164+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T17:44:29.302+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T17:44:29.303+05:30  INFO 24176 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4705 ms
2025-06-16T17:44:29.651+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T17:44:29.828+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@3daf7deb
2025-06-16T17:44:29.835+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T17:44:29.838+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T17:44:30.341+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T17:44:30.352+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T17:44:30.417+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T17:44:32.555+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T17:44:33.261+05:30  INFO 24176 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:44:34.018+05:30  WARN 24176 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T17:44:34.148+05:30  WARN 24176 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T17:44:34.370+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:44:34.767+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:44:34.808+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:44:37.530+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T17:44:38.653+05:30  INFO 24176 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T17:44:38.713+05:30  WARN 24176 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T17:44:38.771+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T17:44:38.778+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T17:44:38.779+05:30  INFO 24176 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T17:44:38.780+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T17:44:38.780+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T17:44:38.780+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T17:44:38.781+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T17:44:38.781+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T17:44:38.781+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T17:44:38.782+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T17:44:38.853+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T17:44:38.854+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T17:44:38.854+05:30  INFO 24176 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T17:44:38.855+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750076078855 with initial instances count: 3
2025-06-16T17:44:38.858+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750076078858, current=UP, previous=STARTING]
2025-06-16T17:44:38.859+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:44:38.868+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T17:44:38.869+05:30  WARN 24176 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:44:38.874+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T17:44:38.875+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T17:44:38.875+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:44:38.899+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 14.612 seconds (process running for 447.59)
2025-06-16T17:44:38.903+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T17:44:41.553+05:30  INFO 24176 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T17:44:41.555+05:30  INFO 24176 --- [workplace-learning] [Thread-7] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T17:44:41.556+05:30  INFO 24176 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750076081556, current=DOWN, previous=UP]
2025-06-16T17:44:41.557+05:30  WARN 24176 --- [workplace-learning] [Thread-7] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:44:41.582+05:30  INFO 24176 --- [workplace-learning] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:44:41.585+05:30  INFO 24176 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T17:44:41.591+05:30  INFO 24176 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T17:44:41.596+05:30  INFO 24176 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T17:44:44.222+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - Re-registering apps/WORKPLACE-LEARNING
2025-06-16T17:44:44.227+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:44:44.250+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-HeartbeatExecutor-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:44:44.608+05:30  INFO 24176 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T17:44:44.613+05:30  INFO 24176 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T17:44:44.614+05:30  INFO 24176 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T17:44:44.729+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T17:44:44.840+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 24176 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T17:44:44.841+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T17:44:46.372+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T17:44:46.612+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 239 ms. Found 28 JPA repository interfaces.
2025-06-16T17:44:46.903+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T17:44:47.055+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:44:47.058+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:44:47.064+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:44:47.067+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:44:47.071+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:44:47.242+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T17:44:47.243+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T17:44:47.243+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T17:44:47.296+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T17:44:47.297+05:30  INFO 24176 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2451 ms
2025-06-16T17:44:47.427+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T17:44:47.506+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@6c3ea451
2025-06-16T17:44:47.506+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T17:44:47.507+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T17:44:47.697+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T17:44:47.701+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T17:44:47.718+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T17:44:48.534+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T17:44:49.252+05:30  INFO 24176 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:44:50.556+05:30  WARN 24176 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T17:44:50.677+05:30  WARN 24176 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T17:44:50.769+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:44:50.993+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:44:51.036+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:44:56.595+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T17:44:57.742+05:30  INFO 24176 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T17:44:57.833+05:30  WARN 24176 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T17:44:57.938+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T17:44:57.947+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T17:44:57.948+05:30  INFO 24176 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T17:44:57.949+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T17:44:57.950+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T17:44:57.951+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T17:44:57.951+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T17:44:57.952+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T17:44:57.953+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T17:44:57.955+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T17:44:58.016+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T17:44:58.017+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T17:44:58.018+05:30  INFO 24176 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T17:44:58.019+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750076098019 with initial instances count: 3
2025-06-16T17:44:58.024+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750076098024, current=UP, previous=STARTING]
2025-06-16T17:44:58.024+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:44:58.039+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T17:44:58.041+05:30  WARN 24176 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:44:58.041+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:44:58.048+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T17:44:58.049+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T17:44:58.085+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 13.346 seconds (process running for 466.777)
2025-06-16T17:44:58.090+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T17:46:23.793+05:30  INFO 24176 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T17:46:23.796+05:30  INFO 24176 --- [workplace-learning] [Thread-13] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T17:46:23.798+05:30  INFO 24176 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750076183798, current=DOWN, previous=UP]
2025-06-16T17:46:23.809+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750076183809, current=UP, previous=DOWN]
2025-06-16T17:46:23.813+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:46:23.828+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:46:23.835+05:30  INFO 24176 --- [workplace-learning] [Thread-13] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:46:23.839+05:30  INFO 24176 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T17:46:23.865+05:30  INFO 24176 --- [workplace-learning] [Thread-13] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T17:46:23.891+05:30  INFO 24176 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T17:46:26.916+05:30  INFO 24176 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T17:46:26.975+05:30  INFO 24176 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T17:46:26.977+05:30  INFO 24176 --- [workplace-learning] [Thread-13] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T17:46:27.124+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T17:46:27.264+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 24176 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T17:46:27.269+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-16T17:46:30.135+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16T17:46:30.547+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 410 ms. Found 28 JPA repository interfaces.
2025-06-16T17:46:30.994+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-16T17:46:31.316+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:46:31.333+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:46:31.346+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-16T17:46:31.368+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:46:31.372+05:30  WARN 24176 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-16T17:46:31.837+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-16T17:46:31.839+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-16T17:46:31.843+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-16T17:46:31.932+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-16T17:46:31.933+05:30  INFO 24176 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 4658 ms
2025-06-16T17:46:32.157+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-16T17:46:32.372+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@13b0a85e
2025-06-16T17:46:32.373+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-16T17:46:32.374+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-16T17:46:32.736+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16T17:46:32.744+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-16T17:46:32.778+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-16T17:46:34.405+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-16T17:46:36.806+05:30  INFO 24176 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:46:38.797+05:30  WARN 24176 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16T17:46:38.986+05:30  WARN 24176 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-16T17:46:39.100+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:46:39.448+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:46:39.528+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-16T17:46:42.900+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-16T17:46:43.799+05:30  INFO 24176 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-16T17:46:43.850+05:30  WARN 24176 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-16T17:46:43.905+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-16T17:46:43.910+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-16T17:46:43.910+05:30  INFO 24176 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-16T17:46:43.911+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-16T17:46:43.911+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-16T17:46:43.912+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-16T17:46:43.912+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-16T17:46:43.912+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-16T17:46:43.913+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-16T17:46:43.913+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-16T17:46:43.948+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-16T17:46:43.949+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-16T17:46:43.950+05:30  INFO 24176 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-16T17:46:43.950+05:30  INFO 24176 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750076203950 with initial instances count: 3
2025-06-16T17:46:43.954+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750076203954, current=UP, previous=STARTING]
2025-06-16T17:46:43.955+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-16T17:46:43.961+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-16T17:46:43.963+05:30  WARN 24176 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:46:43.967+05:30  INFO 24176 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-16T17:46:43.969+05:30  INFO 24176 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-16T17:46:43.970+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-16T17:46:43.994+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 16.861 seconds (process running for 572.687)
2025-06-16T17:46:43.998+05:30  INFO 24176 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-16T17:46:45.325+05:30  INFO 24176 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-16T17:46:45.327+05:30  INFO 24176 --- [workplace-learning] [Thread-19] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-16T17:46:45.327+05:30  INFO 24176 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750076205327, current=DOWN, previous=UP]
2025-06-16T17:46:45.328+05:30  WARN 24176 --- [workplace-learning] [Thread-19] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-16T17:46:45.343+05:30  INFO 24176 --- [workplace-learning] [Thread-19] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16T17:46:45.345+05:30  INFO 24176 --- [workplace-learning] [Thread-19] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-16T17:46:45.348+05:30  INFO 24176 --- [workplace-learning] [Thread-19] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-16T17:46:45.349+05:30  INFO 24176 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-16T17:46:48.361+05:30  INFO 24176 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-16T17:46:48.380+05:30  INFO 24176 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-16T17:46:48.381+05:30  INFO 24176 --- [workplace-learning] [Thread-19] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-16T17:46:48.588+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-16T17:46:48.682+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 24176 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-16T17:46:48.683+05:30  INFO 24176 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
