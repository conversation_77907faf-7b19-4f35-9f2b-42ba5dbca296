2025-06-17T14:33:01.206+05:30  INFO 17528 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 17528 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-17T14:33:01.206+05:30  INFO 17528 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-17T14:33:01.328+05:30  INFO 17528 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-17T14:33:01.328+05:30  INFO 17528 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-17T14:33:03.398+05:30  INFO 17528 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17T14:33:03.716+05:30  INFO 17528 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 305 ms. Found 28 JPA repository interfaces.
2025-06-17T14:33:04.115+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-17T14:33:04.606+05:30  WARN 17528 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-17T14:33:04.606+05:30  WARN 17528 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T14:33:04.619+05:30  WARN 17528 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-17T14:33:04.621+05:30  WARN 17528 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T14:33:04.621+05:30  WARN 17528 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T14:33:05.169+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-17T14:33:05.184+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T14:33:05.200+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-17T14:33:05.291+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T14:33:05.292+05:30  INFO 17528 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3964 ms
2025-06-17T14:33:05.512+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-17T14:33:05.795+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@3f18a840
2025-06-17T14:33:05.795+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-17T14:33:05.818+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-17T14:33:06.401+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17T14:33:06.548+05:30  INFO 17528 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-17T14:33:06.647+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-17T14:33:07.300+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-17T14:33:10.619+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-17T14:33:11.806+05:30  INFO 17528 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17T14:33:12.251+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-17T14:33:13.992+05:30  WARN 17528 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17T14:33:14.084+05:30  WARN 17528 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-17T14:33:14.161+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T14:33:14.371+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T14:33:14.409+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T14:33:17.754+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-17T14:33:19.898+05:30  INFO 17528 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-17T14:33:20.083+05:30  WARN 17528 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-17T14:33:20.368+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-17T14:33:20.466+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-17T14:33:20.525+05:30  INFO 17528 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T14:33:20.611+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-17T14:33:20.622+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-17T14:33:20.648+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-17T14:33:20.664+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-17T14:33:20.670+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-17T14:33:20.682+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-17T14:33:20.684+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-17T14:33:21.890+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-17T14:33:21.914+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-17T14:33:21.937+05:30  INFO 17528 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-17T14:33:21.969+05:30  INFO 17528 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750151001962 with initial instances count: 3
2025-06-17T14:33:22.054+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-17T14:33:22.087+05:30  INFO 17528 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750151002087, current=UP, previous=STARTING]
2025-06-17T14:33:22.108+05:30  INFO 17528 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-17T14:33:22.109+05:30  WARN 17528 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-17T14:33:22.153+05:30  INFO 17528 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-17T14:33:22.219+05:30  INFO 17528 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-17T14:33:22.356+05:30  INFO 17528 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-17T14:33:22.415+05:30  INFO 17528 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 21.867 seconds (process running for 22.568)
2025-06-17T14:38:21.131+05:30  INFO 17528 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T14:43:21.394+05:30  INFO 17528 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T14:48:21.408+05:30  INFO 17528 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T14:53:21.434+05:30  INFO 17528 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T14:57:49.870+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T14:57:49.871+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T14:57:49.877+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-06-17T14:57:50.012+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.complaint.AppealController   : Appeal creation initiated by: 4b25a526-fd3a-4449-8e00-8d50a6ea258e
2025-06-17T14:57:50.015+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.AppealService      : Looking up detailed rejector information for reference: REF-********-306A4E
2025-06-17T14:57:50.015+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.AppealService      : Attempting to fetch managers from Account Service for load balancing...
2025-06-17T14:57:50.638+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.AppealService      : Account Service response - Status: true, Data: [{id=8c6e2610-4a3b-4b58-ad49-283102f7d7ac, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=4012a23a-e0c6-494c-b78f-048177f1a0bf, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=1adb1243-b20d-4a2e-b02f-28d60cbf8795, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}, {id=7df89d26-811d-45c3-a694-e36af54ec7d6, email=<EMAIL>, enabled=true, firstName=null, lastName=null, username=<EMAIL>}]
2025-06-17T14:57:50.639+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.AppealService      : Found 4 managers from Account Service
2025-06-17T14:57:50.776+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.AppealService      : Current manager workloads: {}
2025-06-17T14:57:50.777+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.AppealService      : Selected manager 8c6e2610-4a3b-4b58-ad49-283102f7d7ac with 0 active appeals
2025-06-17T14:57:50.777+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.AppealService      : Assigned appeal to manager with least workload: 8c6e2610-4a3b-4b58-ad49-283102f7d7ac
2025-06-17T14:57:50.927+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.AppealService      : Looking up rejector for appeal reference: REF-********-306A4E
2025-06-17T14:57:50.927+05:30  WARN 17528 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.AppealService      : Could not find rejector for application reference: REF-********-306A4E
2025-06-17T14:58:21.557+05:30  INFO 17528 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T15:00:45.177+05:30  WARN 17528 --- [workplace-learning] [http-nio-8091-exec-9] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.web.bind.MissingRequestHeaderException: Required request header 'X-Authenticated-User' for method parameter type String is not present]
2025-06-17T15:01:16.780+05:30  WARN 17528 --- [workplace-learning] [http-nio-8091-exec-3] ration$PageModule$WarningLoggingModifier : Serializing PageImpl instances as-is is not supported, meaning that there is no guarantee about the stability of the resulting JSON structure!
	For a stable JSON structure, please use Spring Data's PagedModel (globally via @EnableSpringDataWebSupport(pageSerializationMode = VIA_DTO))
	or Spring HATEOAS and Spring Data's PagedResourcesAssembler as documented in https://docs.spring.io/spring-data/commons/reference/repositories/core-extensions.html#core.web.pageables.

2025-06-17T15:01:58.133+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.c.ComplaintController        : Fetching complaints with criteria - role: AGENT, userId: null, companyId: null, status: null, state: null, assignedTo: null, search: null
2025-06-17T15:01:58.134+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.c.c.ComplaintController        : Role-based filtering: role=AGENT, userId=null
2025-06-17T15:01:58.257+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT and userId: null
2025-06-17T15:01:58.258+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : AGENT filter (without userId): state=SUBMITTED (all submitted complaints)
2025-06-17T15:01:58.278+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT and userId: null
2025-06-17T15:01:58.279+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-7] b.o.h.w.w.s.complaint.ComplaintService   : AGENT filter (without userId): state=SUBMITTED (all submitted complaints)
2025-06-17T15:03:21.558+05:30  INFO 17528 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T15:08:07.101+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.c.complaint.AppealController   : Manager communication initiated for appeal id: 28e8aaa0-d228-441f-861f-f0474b0199c3
2025-06-17T15:08:07.113+05:30 ERROR 17528 --- [workplace-learning] [http-nio-8091-exec-6] b.o.h.w.w.c.complaint.AppealController   : Failed to initiate communication for appeal id 28e8aaa0-d228-441f-861f-f0474b0199c3 with exception: Invalid status/state transition
2025-06-17T15:08:21.577+05:30  INFO 17528 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T15:11:04.092+05:30  WARN 17528 --- [workplace-learning] [HikariCP housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Thread starvation or clock leap detected (housekeeper delta=2m27s277ms266µs600ns).
2025-06-17T15:12:58.101+05:30  INFO 17528 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.complaint.AppealController   : Manager communication initiated for appeal id: 28e8aaa0-d228-441f-861f-f0474b0199c3
2025-06-17T15:12:58.104+05:30 ERROR 17528 --- [workplace-learning] [http-nio-8091-exec-10] b.o.h.w.w.c.complaint.AppealController   : Failed to initiate communication for appeal id 28e8aaa0-d228-441f-861f-f0474b0199c3 with exception: Invalid status/state transition
2025-06-17T15:16:23.880+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 17256 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-17T15:16:23.882+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-17T15:16:23.962+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-17T15:16:23.963+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-17T15:16:25.910+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17T15:16:26.202+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 284 ms. Found 28 JPA repository interfaces.
2025-06-17T15:16:26.627+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-17T15:16:27.174+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-17T15:16:27.178+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T15:16:27.186+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-17T15:16:27.193+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T15:16:27.198+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T15:16:27.787+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-17T15:16:27.806+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:16:27.807+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-17T15:16:27.893+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T15:16:27.894+05:30  INFO 17256 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3930 ms
2025-06-17T15:16:28.100+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-17T15:16:28.390+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@72da3058
2025-06-17T15:16:28.394+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-17T15:16:28.413+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-17T15:16:28.966+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17T15:16:29.099+05:30  INFO 17256 --- [workplace-learning] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.5.3.Final
2025-06-17T15:16:29.172+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-17T15:16:29.987+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-17T15:16:34.229+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-17T15:16:35.317+05:30  INFO 17256 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17T15:16:35.871+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-17T15:16:37.692+05:30  WARN 17256 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17T15:16:37.791+05:30  WARN 17256 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-17T15:16:37.874+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T15:16:38.059+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T15:16:38.093+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T15:16:41.599+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-17T15:16:42.978+05:30  INFO 17256 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-17T15:16:43.043+05:30  WARN 17256 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-17T15:16:43.146+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-17T15:16:43.175+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-17T15:16:43.181+05:30  INFO 17256 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T15:16:43.192+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-17T15:16:43.192+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-17T15:16:43.193+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-17T15:16:43.194+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-17T15:16:43.194+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-17T15:16:43.194+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-17T15:16:43.195+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-17T15:16:43.541+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-17T15:16:43.543+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-17T15:16:43.546+05:30  INFO 17256 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-17T15:16:43.548+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750153603547 with initial instances count: 3
2025-06-17T15:16:43.561+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750153603561, current=UP, previous=STARTING]
2025-06-17T15:16:43.562+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-17T15:16:43.567+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-17T15:16:43.569+05:30  WARN 17256 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-17T15:16:43.591+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-17T15:16:43.593+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-17T15:16:43.627+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-17T15:16:43.636+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 20.387 seconds (process running for 21.182)
2025-06-17T15:18:08.757+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17T15:18:08.811+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-17T15:18:08.868+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 28 ms
2025-06-17T15:18:09.329+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.complaint.AppealController   : Manager communication initiated for appeal id: 28e8aaa0-d228-441f-861f-f0474b0199c3
2025-06-17T15:21:43.212+05:30  INFO 17256 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T15:22:17.479+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-5] b.o.h.w.w.c.complaint.AppealController   : Client response recorded for appeal id: 28e8aaa0-d228-441f-861f-f0474b0199c3
2025-06-17T15:26:43.217+05:30  INFO 17256 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T15:27:26.717+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.c.ComplaintController        : Fetching complaints with criteria - role: AGENT, userId: null, companyId: null, status: null, state: null, assignedTo: null, search: null
2025-06-17T15:27:26.717+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.c.c.ComplaintController        : Role-based filtering: role=AGENT, userId=null
2025-06-17T15:27:26.857+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT and userId: null
2025-06-17T15:27:26.858+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : AGENT filter (without userId): state=SUBMITTED (all submitted complaints)
2025-06-17T15:27:26.894+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : Building role-based specification for role: AGENT and userId: null
2025-06-17T15:27:26.894+05:30  INFO 17256 --- [workplace-learning] [http-nio-8091-exec-2] b.o.h.w.w.s.complaint.ComplaintService   : AGENT filter (without userId): state=SUBMITTED (all submitted complaints)
2025-06-17T15:31:43.235+05:30  INFO 17256 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T15:36:43.267+05:30  INFO 17256 --- [workplace-learning] [AsyncResolver-bootstrap-executor-%d] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T15:39:04.454+05:30  INFO 17256 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-17T15:39:04.601+05:30  INFO 17256 --- [workplace-learning] [Thread-1] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-17T15:39:04.717+05:30  INFO 17256 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750154944717, current=DOWN, previous=UP]
2025-06-17T15:39:04.844+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750154944844, current=UP, previous=DOWN]
2025-06-17T15:39:04.884+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-17T15:39:04.939+05:30  INFO 17256 --- [workplace-learning] [Thread-1] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-17T15:39:04.980+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-17T15:39:05.010+05:30  INFO 17256 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-17T15:39:05.148+05:30  INFO 17256 --- [workplace-learning] [Thread-1] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-17T15:39:05.278+05:30  INFO 17256 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-17T15:39:08.433+05:30  INFO 17256 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-17T15:39:08.571+05:30  INFO 17256 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-17T15:39:08.576+05:30  INFO 17256 --- [workplace-learning] [Thread-1] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-17T15:39:08.822+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-17T15:39:09.821+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 17256 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-17T15:39:10.088+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-17T15:39:14.914+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17T15:39:15.496+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 549 ms. Found 28 JPA repository interfaces.
2025-06-17T15:39:17.501+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-17T15:39:18.549+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-17T15:39:18.569+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T15:39:18.638+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-17T15:39:18.648+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T15:39:18.658+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T15:39:19.645+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-17T15:39:19.703+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:39:19.807+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-17T15:39:20.072+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T15:39:20.132+05:30  INFO 17256 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 9817 ms
2025-06-17T15:39:20.588+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-17T15:39:21.140+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@bab3553
2025-06-17T15:39:21.175+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-17T15:39:21.192+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-17T15:39:21.938+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17T15:39:21.997+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-17T15:39:22.147+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-17T15:39:24.680+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-17T15:39:26.463+05:30  INFO 17256 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17T15:39:29.093+05:30  WARN 17256 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17T15:39:29.354+05:30  WARN 17256 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-17T15:39:29.690+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T15:39:30.130+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T15:39:30.273+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T15:39:35.173+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-17T15:39:37.469+05:30  INFO 17256 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-17T15:39:37.560+05:30  WARN 17256 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-17T15:39:37.666+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-17T15:39:37.677+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-17T15:39:37.678+05:30  INFO 17256 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T15:39:37.679+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-17T15:39:37.680+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-17T15:39:37.681+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-17T15:39:37.681+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-17T15:39:37.682+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-17T15:39:37.684+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-17T15:39:37.686+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-17T15:39:37.795+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-17T15:39:37.797+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-17T15:39:37.798+05:30  INFO 17256 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-17T15:39:37.799+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750154977799 with initial instances count: 3
2025-06-17T15:39:37.806+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750154977806, current=UP, previous=STARTING]
2025-06-17T15:39:37.808+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-17T15:39:37.831+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-17T15:39:37.834+05:30  WARN 17256 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-17T15:39:37.838+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-17T15:39:37.844+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-17T15:39:37.846+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-17T15:39:37.892+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 28.912 seconds (process running for 1395.437)
2025-06-17T15:39:37.901+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-06-17T15:39:39.151+05:30  INFO 17256 --- [workplace-learning] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-06-17T15:39:39.153+05:30  INFO 17256 --- [workplace-learning] [Thread-7] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application WORKPLACE-LEARNING with eureka with status DOWN
2025-06-17T15:39:39.154+05:30  INFO 17256 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750154979154, current=DOWN, previous=UP]
2025-06-17T15:39:39.154+05:30  WARN 17256 --- [workplace-learning] [Thread-7] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-17T15:39:39.175+05:30  INFO 17256 --- [workplace-learning] [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-17T15:39:39.177+05:30  INFO 17256 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown initiated...
2025-06-17T15:39:39.182+05:30  INFO 17256 --- [workplace-learning] [Thread-7] com.zaxxer.hikari.HikariDataSource       : HikariCP - Shutdown completed.
2025-06-17T15:39:39.186+05:30  INFO 17256 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Shutting down DiscoveryClient ...
2025-06-17T15:39:42.202+05:30  INFO 17256 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Unregistering ...
2025-06-17T15:39:42.221+05:30  INFO 17256 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - deregister  status: 200
2025-06-17T15:39:42.222+05:30  INFO 17256 --- [workplace-learning] [Thread-7] com.netflix.discovery.DiscoveryClient    : Completed shut down of DiscoveryClient
2025-06-17T15:39:42.344+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Workplace Learning Service Management initiated
2025-06-17T15:39:42.443+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Starting WorkplaceLearningApplication using Java 21.0.6 with PID 17256 (C:\project\ehrdcc-project\workplace-learning\target\classes started by dell in C:\project\ehrdcc-project\workplace-learning)
2025-06-17T15:39:42.444+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : The following 1 profile is active: "dev"
2025-06-17T15:39:43.752+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17T15:39:44.076+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 322 ms. Found 28 JPA repository interfaces.
2025-06-17T15:39:44.361+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.cloud.context.scope.GenericScope     : BeanFactory id=5a6c540e-2afa-3476-a61e-6b039c0daea9
2025-06-17T15:39:44.507+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-17T15:39:44.513+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T15:39:44.520+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-06-17T15:39:44.527+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T15:39:44.530+05:30  WARN 17256 --- [workplace-learning] [restartedMain] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [loadBalancerWebClientBuilderBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-06-17T15:39:44.765+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8091 (http)
2025-06-17T15:39:44.766+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-17T15:39:44.767+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-17T15:39:44.829+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-17T15:39:44.830+05:30  INFO 17256 --- [workplace-learning] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2381 ms
2025-06-17T15:39:45.003+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Starting...
2025-06-17T15:39:45.196+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariCP - Added connection org.postgresql.jdbc.PgConnection@599c9a11
2025-06-17T15:39:45.197+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariCP - Start completed.
2025-06-17T15:39:45.198+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Database available at '***************************************************'
2025-06-17T15:39:45.403+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17T15:39:45.408+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-06-17T15:39:45.426+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-17T15:39:46.937+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-17T15:39:47.661+05:30  INFO 17256 --- [workplace-learning] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17T15:39:48.700+05:30  WARN 17256 --- [workplace-learning] [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17T15:39:48.775+05:30  WARN 17256 --- [workplace-learning] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-06-17T15:39:48.830+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'ACCOUNT-SERVICE' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T15:39:49.023+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'WORKFLOW' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T15:39:49.093+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.openfeign.FeignClientFactoryBean   : For 'account-service' URL not provided. Will try picking an instance via load-balancing.
2025-06-17T15:39:51.601+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 18 endpoints beneath base path '/actuator'
2025-06-17T15:39:53.053+05:30  INFO 17256 --- [workplace-learning] [restartedMain] DiscoveryClientOptionalArgsConfiguration : Eureka HTTP Client uses RestTemplate.
2025-06-17T15:39:53.134+05:30  WARN 17256 --- [workplace-learning] [restartedMain] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-17T15:39:53.223+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-06-17T15:39:53.229+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Initializing Eureka in region us-east-1
2025-06-17T15:39:53.229+05:30  INFO 17256 --- [workplace-learning] [restartedMain] c.n.d.s.r.aws.ConfigClusterResolver      : Resolving eureka endpoints via configuration
2025-06-17T15:39:53.230+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Disable delta property : false
2025-06-17T15:39:53.231+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Single vip registry refresh property : null
2025-06-17T15:39:53.231+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Force full registry fetch : false
2025-06-17T15:39:53.233+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application is null : false
2025-06-17T15:39:53.234+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Registered Applications size is zero : true
2025-06-17T15:39:53.235+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Application version is -1: true
2025-06-17T15:39:53.236+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Getting all instance registry info from the eureka server
2025-06-17T15:39:53.296+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : The response status is 200
2025-06-17T15:39:53.297+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Starting heartbeat executor: renew interval is: 5
2025-06-17T15:39:53.297+05:30  INFO 17256 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-06-17T15:39:53.298+05:30  INFO 17256 --- [workplace-learning] [restartedMain] com.netflix.discovery.DiscoveryClient    : Discovery Client initialized at timestamp 1750154993298 with initial instances count: 3
2025-06-17T15:39:53.303+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : Saw local status change event StatusChangeEvent [timestamp=1750154993302, current=UP, previous=STARTING]
2025-06-17T15:39:53.303+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091: registering service...
2025-06-17T15:39:53.340+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application WORKPLACE-LEARNING with eureka with status UP
2025-06-17T15:39:53.343+05:30  INFO 17256 --- [workplace-learning] [DiscoveryClient-InstanceInfoReplicator-%d] com.netflix.discovery.DiscoveryClient    : DiscoveryClient_WORKPLACE-LEARNING/DESKTOP-MIJ80IO.mshome.net:workplace-learning:8091 - registration status: 204
2025-06-17T15:39:53.345+05:30  WARN 17256 --- [workplace-learning] [restartedMain] c.n.discovery.InstanceInfoReplicator     : Ignoring onDemand update due to rate limiter
2025-06-17T15:39:53.353+05:30  INFO 17256 --- [workplace-learning] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8091 (http) with context path '/'
2025-06-17T15:39:53.356+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8091
2025-06-17T15:39:53.403+05:30  INFO 17256 --- [workplace-learning] [restartedMain] b.o.h.w.w.WorkplaceLearningApplication   : Started WorkplaceLearningApplication in 11.051 seconds (process running for 1410.949)
2025-06-17T15:39:53.428+05:30  INFO 17256 --- [workplace-learning] [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
