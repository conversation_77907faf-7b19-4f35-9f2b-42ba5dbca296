package bw.org.hrdc.weblogic.workplacelearning.api;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import bw.org.hrdc.weblogic.workplacelearning.common.config.FeignConfig;
import bw.org.hrdc.weblogic.workplacelearning.dto.AgentDto;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;


@FeignClient(name = "account-service", path = "/api/v1/user/backoffice", fallback = AccountClientFallback.class, configuration = FeignConfig.class, contextId = "accountClient")
public interface AccountClient {

    /**
     * ✅ CLIENT FEEDBACK: Use existing implementation to get users by roles
     */
    @GetMapping("/users-by-role/{role}")
    ApiResponse<?> fetchAllActiveUsers(@PathVariable("role") String role);

    /**
     * ✅ CLIENT FEEDBACK: Agent lead is a user, use existing function to get user by id
     * Using the same pattern as CompanyClient which already works
     */
    @GetMapping("/user/{id}")
    ApiResponse<?> fetchUserById(@PathVariable String id);
}
