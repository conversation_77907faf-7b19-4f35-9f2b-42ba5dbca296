package bw.org.hrdc.weblogic.workplacelearning.api;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import bw.org.hrdc.weblogic.workplacelearning.common.config.FeignConfig;
import bw.org.hrdc.weblogic.workplacelearning.dto.AgentDto;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;


@FeignClient(name = "account-service", path = "/api/v1/user/backoffice", fallback = AccountClientFallback.class, configuration = FeignConfig.class, contextId = "accountClient")
public interface AccountClient {

    /**
     * Fetches all ACTIVE users with the specified role
     * Used for: Agent/Manager assignment, load balancing operations
     * Endpoint: /api/v1/user/backoffice/users-by-role/{role}
     */
    @GetMapping("/users-by-role/{role}")
    ApiResponse<?> fetchAllActiveUsers(@PathVariable("role") String role);

    /**
     * Fetches all users (active and inactive) with the specified role
     * Used for: User validation, role verification operations
     * Endpoint: /api/v1/user/backoffice/{role}
     */
    @GetMapping("/{role}")
    ApiResponse<?> fetchAllUsersByRole(@PathVariable String role);
}
