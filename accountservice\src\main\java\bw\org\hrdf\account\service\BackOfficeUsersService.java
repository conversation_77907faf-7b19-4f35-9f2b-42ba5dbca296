package bw.org.hrdf.account.service;

import bw.org.hrdf.account.dto.BackOfficeProfile;
import bw.org.hrdf.account.models.otp.OtpRequest;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.ClientResource;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.RoleResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * eHRDF Account Service
 * This service provides service to create, retrieve
 * update & delete account object in eHRDF.
 *
 * <AUTHOR>
 *
 */
@RefreshScope
@Service
public class BackOfficeUsersService {

    @Autowired
    private Keycloak keycloak;

    @Value("${keycloak.realm}")
    private String realm;

    @Value("${keycloak.client-id}")
    private String clientId;

    @Value("${keycloak.client-secret}")
    private String clientSecret;

    @Autowired
    private KafkaTemplate<String, OtpRequest> kafkaTemplate;

    @Autowired
    private RestTemplate restTemplate;

    private static final Logger logger = LoggerFactory.getLogger(BackOfficeUsersService.class);

    public List<BackOfficeProfile> retrieveUsersByRoles(String roleName){
        RealmResource realmResource = keycloak.realm(realm);

        Optional<ClientResource> clientResource = realmResource.clients().findAll().stream()
                .filter(client -> client.getClientId().equals(clientId))
                .findFirst()
                .map(client -> realmResource.clients().get(client.getId()));

        if (clientResource.isPresent()) {
            RoleResource roleResource = clientResource.get().roles().get(roleName);


            return roleResource.getUserMembers().stream()
                    .map(user -> new BackOfficeProfile(
                            user.getId(),
                            user.getEmail(),
                            user.isEnabled(),
                            user.getFirstName(),
                            user.getLastName(),
                            user.getUsername()
                    ))
                    .toList();
        } else {
            throw new RuntimeException("Client with ID " + clientId + " not found");
        }
    }

    public List<BackOfficeProfile> retrieveUsersByRoleAndGroup(String roleName, String group) {
        RealmResource realmResource = keycloak.realm(realm);

        Optional<ClientResource> clientResource = realmResource.clients().findAll().stream()
                .filter(client -> client.getClientId().equals(clientId))
                .findFirst()
                .map(client -> realmResource.clients().get(client.getId()));

        if (clientResource.isEmpty()) {
            throw new RuntimeException("Client with ID " + clientId + " not found");
        }

        RoleResource roleResource = clientResource.get().roles().get(roleName.toLowerCase());
        List<UserRepresentation> users = roleResource.getUserMembers();

        if (group != null) {
            // Filter users by group
            List<UserRepresentation> filteredUsers = users.stream()
                    .filter(user -> userBelongsToGroup(user, group.toLowerCase(), realmResource))
                    .toList();

            return mapToBackOfficeProfiles(filteredUsers);
        }

        return mapToBackOfficeProfiles(users);
    }

    private boolean userBelongsToGroup(UserRepresentation user, String group, RealmResource realmResource) {
        List<GroupRepresentation> userGroups = realmResource.users().get(user.getId()).groups();
        return userGroups.stream().anyMatch(g -> g.getName().equalsIgnoreCase(group));
    }

    private List<BackOfficeProfile> mapToBackOfficeProfiles(List<UserRepresentation> users) {
        return users.stream()
                .map(user -> new BackOfficeProfile(
                        user.getId(),
                        user.getEmail(),
                        user.isEnabled(),
                        user.getFirstName(),
                        user.getLastName(),
                        user.getUsername()
                ))
                .toList();
    }

    /**
     * Search users by partial assignee match
     * @param assignee Partial assignee to search for
     * @return List of matching UserRepresentation objects
     */
    public List<UserRepresentation> searchUsersByPartialUsername(String assignee) {
        if (assignee == null || assignee.isEmpty()) {
            return List.of();
        }
        
        RealmResource realmResource = keycloak.realm(realm);
        
        // Keycloak's search method already supports partial matching
        // Using a reasonable default of 20 results
        return realmResource.users().search(assignee, 0, 100);
    }

    /**
     * Retrieve a user by their ID
     * @param userId The ID of the user to retrieve
     * @return UserRepresentation object or null if not found
     */
    public UserRepresentation getUserById(String userId) {
        if (userId == null || userId.isEmpty()) {
            return null;
        }
        
        try {
            RealmResource realmResource = keycloak.realm(realm);
            return realmResource.users().get(userId).toRepresentation();
        } catch (Exception e) {
            logger.error("Error retrieving user by ID {}: {}", userId, e.getMessage());
            return null;
        }
    }

    /**
     * Get all roles for a user, including realm roles and client roles
     * @param userId The ID of the user
     * @return Map containing lists of roles by type
     */
    public Map<String, List<String>> getUserRoles(String userId) {
        try {
            Map<String, List<String>> userRoles = new HashMap<>();
            RealmResource realmResource = keycloak.realm(realm);
            UserResource userResource = realmResource.users().get(userId);
            
            // Get all role mappings
            MappingsRepresentation userActiveRoles = userResource.roles().getAll();
            
            // Get user groups
            List<String> userActiveGroups = userResource.groups().stream()
                    .map(group -> group.getName().replace("-", "_").toUpperCase())
                    .collect(Collectors.toList());
            
            // Get realm roles
            List<String> realmRoleNames = Optional.ofNullable(userActiveRoles.getRealmMappings())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(role -> role.getName().replace("-", "_").toUpperCase())
                    .collect(Collectors.toList());
            
            // Get client roles
            List<ClientRepresentation> clients = realmResource.clients().findByClientId(clientId);
            Set<String> allClientRoles = new HashSet<>();
            
            if (!clients.isEmpty()) {
                for (ClientRepresentation client : clients) {
                    List<RoleRepresentation> clientRoles = userResource.roles().clientLevel(client.getId()).listAll();
                    clientRoles.forEach(role -> allClientRoles.add(role.getName().replace("-", "_").toUpperCase()));
                }
            }
            
            // Add all role types to the response
            userRoles.put("realmRoles", realmRoleNames);
            userRoles.put("clientRoles", new ArrayList<>(allClientRoles));
            userRoles.put("groups", userActiveGroups);
            
            // For backward compatibility, also include the "roles" key with client roles
            userRoles.put("roles", new ArrayList<>(allClientRoles));
            
            return userRoles;
        } catch (Exception e) {
            logger.error("Error retrieving roles for user {}: {}", userId, e.getMessage());
            throw new RuntimeException("Failed to retrieve roles for user: " + userId, e);
        }
    }
}

