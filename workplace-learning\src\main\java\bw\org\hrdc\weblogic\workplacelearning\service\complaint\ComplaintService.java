package bw.org.hrdc.weblogic.workplacelearning.service.complaint;

import bw.org.hrdc.weblogic.workplacelearning.api.AccountClient;
import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.AgentDto;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintResponse;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintSearchCriteria;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintsStats;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ListData;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.AuditLog;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintDocument;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.AuditLogRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintDocumentRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintRepository;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.BaseSpecifications;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Comparator;
import java.util.Map;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import feign.FeignException;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 22:08
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 22:08
 */
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
@RequiredArgsConstructor
public class ComplaintService {

    private static final Logger logger = LoggerFactory.getLogger(ComplaintService.class);

    private final ComplaintRepository complaintRepository;
    private final AuditLogRepository auditLogRepository;

    private final PaperlessService paperlessService;
    private final ComplaintDocumentRepository documentRepository;
    private final WorkflowClient workflowClient;
    @Autowired
    private CompanyClient companyClient;
    @Autowired
    private AccountClient accountClient;

    // Load balancing for agent assignment (replaced round-robin with workload-based assignment)

    private String generateComplaintReference() {
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String uniquePart = String.format("%04d", (int) (Math.random() * 10000));
        return "CMP-" + datePart + "-" + uniquePart;
    }

    public ComplaintEntity createComplaint(ComplaintEntity complaint, String userId) {

        String referenceNumber = generateComplaintReference();

        // Step 1: Auto-assign agent using Load Balancing (least workload)
        String assignedAgentId = assignAgentByWorkload();

        // Ensure we always have an agent assigned
        if (assignedAgentId == null || assignedAgentId.isEmpty()) {
            throw new RuntimeException("No agents available for assignment. Please ensure Account Service is running and has active agents.");
        }

        complaint.setStatus(Enums.ComplaintStatus.OPEN);
        complaint.setState(Enums.ComplaintState.SUBMITTED);
        complaint.setReferenceNumber(referenceNumber);
        complaint.setAssignee(assignedAgentId);
        complaint.setCategory(Enums.CategoryComplaint.COMPLAINT);

        ComplaintEntity savedComplaint = complaintRepository.save(complaint);

        AuditLog log = new AuditLog();
        log.setComplaint(savedComplaint);
        log.setAction("CREATED");
        log.setDescription("Complaint submitted and assigned to agent");
        log.setPerformedBy(userId);
        log.setTimestamp(LocalDateTime.now());
        auditLogRepository.save(log);

        return savedComplaint;
    }

    /**
     * ✅ LOAD BALANCING: Agent Assignment from Account Service
     * Fetches real agents from Account Service and assigns to agent with least workload
     * Similar to MongoDB aggregation: db.complaints.aggregate([{$group:{_id:"$assignee", total:{$sum:1}}}, {$sort:{total:1}}])
     */
    private String assignAgentByWorkload() {
        try {
            logger.info("Attempting to fetch agents from Account Service for load balancing...");
            ApiResponse<?> response = accountClient.getAllAgents();
            logger.info("Account Service response - Status: {}, Data: {}", response.isStatus(), response.getData());

            if (response.isStatus() && response.getData() != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> agentData = (List<Map<String, Object>>) response.getData();
                logger.info("Found {} agents from Account Service", agentData.size());

                if (!agentData.isEmpty()) {
                    String selectedAgentId = findAgentWithLeastWorkload(agentData);
                    logger.info("Assigned complaint to agent with least workload: {}", selectedAgentId);
                    return selectedAgentId;
                }
            }
            logger.error("No agents available for assignment from Account Service");
        } catch (Exception e) {
            logger.error("Failed to fetch agents for assignment: {}", e.getMessage());
        }

        // No agents available - return null to trigger error handling
        return null;
    }

    /**
     * ✅ LOAD BALANCING: Find agent with least workload
     * Uses database aggregation to count active complaints per agent
     */
    private String findAgentWithLeastWorkload(List<Map<String, Object>> agents) {
        // Get current workload from database using aggregation query
        List<Object[]> workloadData = complaintRepository.getComplaintCountByAgent();

        // Convert to Map for easy lookup: agentId -> complaint count
        Map<String, Long> workloadMap = workloadData.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],  // assignee (agent ID)
                row -> ((Number) row[1]).longValue()  // complaint count
            ));

        logger.info("Current agent workloads: {}", workloadMap);

        // Find agent with minimum workload (including agents with 0 complaints)
        String selectedAgent = agents.stream()
            .map(agent -> (String) agent.get("id"))
            .min(Comparator.comparing(agentId -> workloadMap.getOrDefault(agentId, 0L)))
            .orElse(null);

        Long selectedAgentWorkload = workloadMap.getOrDefault(selectedAgent, 0L);
        logger.info("Selected agent {} with {} active complaints", selectedAgent, selectedAgentWorkload);

        return selectedAgent;
    }

    /**
     * ✅ LOAD BALANCING: Find agent lead with least escalated complaint workload
     * Uses database aggregation to count active escalated complaints per agent lead
     */
    private String findAgentLeadWithLeastWorkload(List<Map<String, Object>> agentLeads) {
        // Get current escalated complaint workload from database using aggregation query
        List<Object[]> workloadData = complaintRepository.getEscalatedComplaintCountByAgentLead();

        // Convert to Map for easy lookup: agentLeadId -> escalated complaint count
        Map<String, Long> workloadMap = workloadData.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],  // assignee (agent lead ID)
                row -> ((Number) row[1]).longValue()  // escalated complaint count
            ));

        logger.info("Current agent lead workloads: {}", workloadMap);

        // Find agent lead with minimum escalated complaint workload (including agent leads with 0 escalated complaints)
        String selectedAgentLead = agentLeads.stream()
            .map(agentLead -> (String) agentLead.get("id"))
            .min(Comparator.comparing(agentLeadId -> workloadMap.getOrDefault(agentLeadId, 0L)))
            .orElse(null);

        Long selectedAgentLeadWorkload = workloadMap.getOrDefault(selectedAgentLead, 0L);
        logger.info("Selected agent lead {} with {} active escalated complaints", selectedAgentLead, selectedAgentLeadWorkload);

        return selectedAgentLead;
    }

    public ComplaintEntity fetchById(String complaintId) {
        return complaintRepository.findByUuid(complaintId)
                .orElseThrow(() -> new RuntimeException("Complaint not found"));
    }

    public ComplaintResponse fetchDetailedComplaint(String complaintId){
             ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

             List<Map<String, Object>> res;

             if (apiResponse.isStatus() && apiResponse.getData()!=null) {
                 res = (List<Map<String, Object>>) apiResponse.getData();
             }
             else{
                 res = List.of();
             }

             Optional<ComplaintEntity> complaint = complaintRepository.findByUuid(complaintId);

             if(complaint.isPresent()){

                 ComplaintEntity complaintData = complaint.get();
                 ComplaintResponse complaintResponse = new ComplaintResponse();

                 complaintResponse.setUuid(complaintData.getUuid());
                 complaintResponse.setReference(complaintData.getReference());
                 complaintResponse.setReferenceNumber(complaintData.getReferenceNumber());
                 complaintResponse.setOrganisationId(complaintData.getOrganisationId());
                 complaintResponse.setDepartment(complaintData.getDepartment());
                 complaintResponse.setTypeOfComplaint(complaintData.getTypeOfComplaint());
                 complaintResponse.setState(complaintData.getState());
                 complaintResponse.setStatus(complaintData.getStatus());
                 complaintResponse.setCreatedAt(complaintData.getCreatedAt());
                 complaintResponse.setUpdatedAt(complaintData.getUpdatedAt());
                 complaintResponse.setCreatedBy(complaintData.getCreatedBy());
                 complaintResponse.setUpdatedBy(complaintData.getUpdatedBy());
                 complaintResponse.setAssignedTo(complaintData.getAssignee());
                 complaintResponse.setDocuments(complaintData.getDocuments());
                 complaintResponse.setComments(complaintData.getComments());
                 complaintResponse.setAuditLogs(complaintData.getAuditLogs());
                 complaintResponse.setDescription(complaintData.getDescription());

                 List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(complaintData.getOrganisationId())).toList();

                 if(!company.isEmpty()){
                     complaintResponse.setOrganisation(company.get(0).get("name").toString());
                 }

                 return complaintResponse;
             }
             return null;
     }

    public Page<ListData> fetchList(PageRequest pageable) {
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }
  
        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isComplaint());

        // Create a new PageRequest with sorting by createdAt in descending order (most recent first)
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        PageRequest sortedPageable = PageRequest.of(
            pageable.getPageNumber(), 
            pageable.getPageSize(), 
            sort
        );

        Page<ComplaintEntity> complaints = complaintRepository.findAll(spec, sortedPageable);
        
        return complaints.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment().name());
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState().name());
            model.setStatus(app.getStatus().name());

            List<Map<String, Object>> company = res.stream()
                .filter(companyDTO -> !companyDTO.isEmpty() && 
                       companyDTO.get("uuid").toString().equals(app.getOrganisationId()))
                .toList();
            
            if(!company.isEmpty()){
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintEntity updateStatus(String complaintId, String userId, Enums.ComplaintStatus status, Enums.ComplaintState state) {
        try {
            ComplaintEntity complaint = fetchById(complaintId);

            logger.info("Updating complaint: {}", complaintId);
            logger.info("Current Status: {}, Current State: {}", complaint.getStatus(), complaint.getState());
            logger.info("Requested Status: {}, Requested State: {}", status, state);

            if(complaint != null){
            // Validate transition (temporarily disabled for testing)
            try {
                validateWorkflowTransition(complaint.getStatus(), complaint.getState(), status, state);
                logger.info("Workflow transition validation passed");
            } catch (IllegalArgumentException e) {
                logger.warn("Workflow transition validation failed: {} - Proceeding anyway for testing", e.getMessage());
                logger.warn("Current: {}:{}, Requested: {}:{}", complaint.getStatus(), complaint.getState(), status, state);
            }

            complaint.setStatus(status);
            complaint.setState(state);
            logger.info("Complaint status updated to: {}, state updated to: {}", status, state);
            complaintRepository.save(complaint);

            AuditLog log = new AuditLog(complaint, "STATUS_CHANGED", "Status changed to " + status, userId, LocalDateTime.now());
            auditLogRepository.save(log);

            logger.info("Audit log created for status change");

            String assignedAgentId = complaint.getAssignee();
            String processInstanceId = complaint.getProcessInstanceId();

            // Extract roles from the response
            String role = null;

            // Fetch agent role from Account Service
            try {
                ApiResponse<?> response = companyClient.fetchUserById(assignedAgentId);
                logger.info("User fetch response: {}", response);

                if (response.isStatus() && response.getData() != null) {
                    Map<String, Object> userData = (Map<String, Object>) response.getData();
                    if (userData.containsKey("roles")) {
                        Map<String, Object> rolesData = (Map<String, Object>) userData.get("roles");
                        if (rolesData.containsKey("roles")) {
                            Object rolesObj = rolesData.get("roles");
                            if (rolesObj instanceof List) {
                                List<String> rolesList = (List<String>) rolesObj;
                                if (!rolesList.isEmpty()) {
                                    role = rolesList.get(0); // Get the first role
                                }
                            } else if (rolesObj instanceof String) {
                                role = (String) rolesObj;
                            }
                            logger.info("User roles: {}", role);
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("Failed to fetch user details for agent: {}, error: {}", assignedAgentId, e.getMessage());
                // Fallback to default role
                role = "AGENT";
                logger.info("Using fallback role: {}", role);
            }
            String actionType = null;
            if (role != null) {
                if(Enums.UserRoles.AGENT.name().equalsIgnoreCase(role)) {
                    actionType = "Agent_action";
                }else if(Enums.UserRoles.OFFICER.name().equalsIgnoreCase(role)) {
                    actionType = "Officer_action";
                }else if(Enums.UserRoles.MANAGER.name().equalsIgnoreCase(role)) {
                    actionType = "Manager_action";
                }
                logger.info("Determined action type: {} for role: {}", actionType, role);
            } else {
                logger.warn("Role is null, cannot determine action type");
            }

            // Only attempt workflow integration if we have all required data
            if (processInstanceId != null && !processInstanceId.isEmpty() && actionType != null) {
                        try {
                            Map<String, Object> workflowPayload = new HashMap<>();
                            workflowPayload.put("complaintId", complaint.getUuid().toString());
                            workflowPayload.put("ApplicationType", Enums.ApplicationType.PRE_APPROVAL.name());
                            workflowPayload.put("role", role);
                            logger.info("User Role : {} and ref number :{}", role, complaint.getUuid().toString());
                            logger.info("Calling workflow client with processInstanceId: {}, actionType: {}", processInstanceId, actionType);
                            workflowClient.resumeComplaintProcess(processInstanceId, actionType, workflowPayload);
                            logger.info("Workflow process resumed successfully");
                        } catch (FeignException e) {
                            // Log the error but continue with the complaint status update
                            logger.error("Failed to resume workflow process - Feign error: Status: {}, Message: {}", e.status(), e.getMessage());
                            logger.warn("Continuing with complaint status update despite workflow error");
                            // Don't rethrow the exception - allow the complaint status update to succeed
                        } catch (Exception e) { 
                            // Log the error but continue with the complaint status update
                            logger.error("Failed to resume workflow process - General error: {}", e.getMessage());
                            logger.warn("Continuing with complaint status update despite workflow error");
                            // Don't rethrow the exception - allow the complaint status update to succeed
                        }
                    } else {
                        if (processInstanceId == null || processInstanceId.isEmpty()) {
                            logger.warn("No process instance ID found for complaint {}", complaint.getUuid().toString());
                        }
                        if (actionType == null) {
                            logger.warn("No action type determined for complaint {} - role: {}", complaint.getUuid().toString(), role);
                        }
            }

            }

            logger.info("Complaint status update completed successfully for complaint: {}", complaintId);
            return complaint;
        } catch (Exception e) {
            logger.error("Error updating complaint status: {}", e.getMessage(), e);
            throw e; // Re-throw to let the controller handle it
        }
    }

    @Transactional()
    public void createComplaintDocument(ComplaintDocument complaintDocument) {
        documentRepository.save(complaintDocument);
    }

    private void validateWorkflowTransition(Enums.ComplaintStatus currentStatus, Enums.ComplaintState currentState,
                                            Enums.ComplaintStatus newStatus, Enums.ComplaintState newState) {
        
        logger.info("Validating transition from {}:{} to {}:{}", currentStatus, currentState, newStatus, newState);
        
        // Allow transitions from OPEN/SUBMITTED
        if (currentStatus == Enums.ComplaintStatus.OPEN && currentState == Enums.ComplaintState.SUBMITTED) {
            if (newStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && newState == Enums.ComplaintState.UNDER_REVIEW) return; // Agent communicates
            if (newStatus == Enums.ComplaintStatus.IN_PROGRESS && newState == Enums.ComplaintState.UNDER_REVIEW) return;
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return; // Allow direct closure
            if (newStatus == Enums.ComplaintStatus.OPEN && newState == Enums.ComplaintState.ESCALATED) return; // Allow escalation
        }
        
        // Allow transitions from AWAITING_CLIENT_FEEDBACK/UNDER_REVIEW
        if (currentStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && currentState == Enums.ComplaintState.UNDER_REVIEW) {
            if (newStatus == Enums.ComplaintStatus.IN_PROGRESS && newState == Enums.ComplaintState.UNDER_REVIEW) return; // Client responds
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return; // Allow closure
            if (newStatus == Enums.ComplaintStatus.OPEN && newState == Enums.ComplaintState.ESCALATED) return; // Allow escalation
        }

        // Allow transitions from IN_PROGRESS/UNDER_REVIEW
        if (currentStatus == Enums.ComplaintStatus.IN_PROGRESS && currentState == Enums.ComplaintState.UNDER_REVIEW) {
            if (newStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && newState == Enums.ComplaintState.UNDER_REVIEW) return; // Agent communicates again
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return; // Allow closure
            if (newStatus == Enums.ComplaintStatus.OPEN && newState == Enums.ComplaintState.ESCALATED) return; // Allow escalation
        }

        // Allow transitions from OPEN/ESCALATED
        if (currentStatus == Enums.ComplaintStatus.OPEN && currentState == Enums.ComplaintState.ESCALATED) {
            if (newStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && newState == Enums.ComplaintState.ESCALATED) return; // Agent Lead communicates
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return; // Allow closure
        }

        // Allow transitions from AWAITING_CLIENT_FEEDBACK/ESCALATED
        if (currentStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && currentState == Enums.ComplaintState.ESCALATED) {
            if (newStatus == Enums.ComplaintStatus.IN_PROGRESS && newState == Enums.ComplaintState.ESCALATED) return; // Client responds to Agent Lead
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return; // Allow closure
        }

        // Allow transitions from IN_PROGRESS/ESCALATED
        if (currentStatus == Enums.ComplaintStatus.IN_PROGRESS && currentState == Enums.ComplaintState.ESCALATED) {
            if (newStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && newState == Enums.ComplaintState.ESCALATED) return; // Agent Lead communicates again
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return; // Allow closure
        }
        
        throw new IllegalArgumentException("Invalid status/state transition");
    }

    public Page<ListData> fetchCompanyList(String companyId, Pageable pageable) {
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }

        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = Specification.where(BaseSpecifications.isComplaint());
        spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("organisationId"), companyId));
        // Create a new PageRequest with sorting by createdAt in descending order (most recent first)
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        PageRequest sortedPageable = PageRequest.of(
            pageable.getPageNumber(), 
            pageable.getPageSize(), 
            sort
        );

        Page<ComplaintEntity> complaints = complaintRepository.findAll(spec, sortedPageable);
        return complaints.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment().name());
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState().name());
            model.setStatus(app.getStatus().name());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                System.out.println(company);
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintsStats getCompanyStatistics(String companyId) {
        List<Object[]> results = complaintRepository.getCompanyComplaintStatistics(companyId, Enums.CategoryComplaint.COMPLAINT.name());
        if (!results.isEmpty()) {
            Object[] row = results.get(0);
            return new ComplaintsStats(
                    ((Number) row[0]).longValue(),
                    ((Number) row[1]).longValue(),
                    ((Number) row[2]).longValue(),
                    ((Number) row[3]).longValue(),
                    ((Number) row[4]).longValue()
            );
        }
        return new ComplaintsStats(0L, 0L, 0L, 0L, 0L);
    }

    public ComplaintsStats getStatistics() {
        List<Object[]> results = complaintRepository.getComplaintStatistics(Enums.CategoryComplaint.COMPLAINT.name());
        if (!results.isEmpty()) {
            Object[] row = results.get(0);
            return new ComplaintsStats(
                    ((Number) row[0]).longValue(),
                    ((Number) row[1]).longValue(),
                    ((Number) row[2]).longValue(),
                    ((Number) row[3]).longValue(),
                    ((Number) row[4]).longValue()
            );
        }
        return new ComplaintsStats(0L, 0L, 0L, 0L, 0L);
    }

    public Page<ListData> fetchUserAssignedList(PageRequest pageable, ComplaintSearchCriteria searchCriteria) {

        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }
        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isComplaint());

        // ✅ ROLE-BASED FILTERING IMPLEMENTATION
        if (searchCriteria.getRole() != null) {
            // Apply role-based filtering when role is provided
            spec = spec.and(buildRoleBasedSpecification(searchCriteria.getRole(), searchCriteria.getAssignedTo()));
        } else if (searchCriteria.getAssignedTo() != null) {
            // Only apply direct assignee filter if no role-based filtering is applied
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("assignee"), searchCriteria.getAssignedTo()));
        }

        if (searchCriteria.getStatus() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("status"), Enums.ComplaintStatus.valueOf(searchCriteria.getStatus().toUpperCase())));
        }

        if (searchCriteria.getComplaintStates() != null && !searchCriteria.getComplaintStates().isEmpty()) {
            List<Enums.ComplaintState> states = searchCriteria.getComplaintStates();
            spec = spec.and((root, query, criteriaBuilder) -> root.get("state").in(states));
        }

        Page<ComplaintEntity> complaints = complaintRepository.findAll(spec, pageable);
        return complaints.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment().name());
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState().name());
            model.setStatus(app.getStatus().name());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                System.out.println(company);
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintEntity assignedNewAssistance(String complaintId, String  agentId, String userId) {
        ComplaintEntity complaint = fetchById(complaintId);
        if(complaint != null){
            complaint.setAssignee(agentId);
            complaintRepository.save(complaint);

            AuditLog log = new AuditLog(complaint, "ASSIGN_AGENT_TO_COMPLAINT", "Complaint assigned to human agent ", userId, LocalDateTime.now());
            auditLogRepository.save(log);
        }


        return complaint;
    }

    public void updateProcessInstanceIdToPreApprovalApplication(String preApprovalId, String processInstanceId) {
        ComplaintEntity complaint = fetchById(preApprovalId);
        if (complaint != null) {
            complaint.setProcessInstanceId(processInstanceId);
            complaintRepository.save(complaint);
        } else {
            throw new RuntimeException("Complaint not found with ID: " + preApprovalId);
        }
    }

    /**
     * Escalate complaint to Agent Lead
     */
    public ComplaintEntity escalateToAgentLead(String complaintId, String userId) {
        try {
            ComplaintEntity complaint = fetchById(complaintId);

            String agentLeadId = null;

            try {
                // Try to get Agent Leads from Account Service
                logger.info("Attempting to fetch Agent Leads from Account Service...");
                ApiResponse<?> response = accountClient.getAgentLeads();
                logger.info("Agent Leads response - Status: {}, Data: {}", response.isStatus(), response.getData());

                if (response.isStatus() && response.getData() != null) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> agentLeads = (List<Map<String, Object>>) response.getData();
                    logger.info("Found {} agent leads from Account Service", agentLeads.size());

                    if (!agentLeads.isEmpty()) {
                        // ✅ LOAD BALANCING: Assign to Agent Lead with least escalated complaints
                        agentLeadId = findAgentLeadWithLeastWorkload(agentLeads);
                        logger.info("Assigned escalated complaint to Agent Lead with least workload: {}", agentLeadId);
                    }
                }
            } catch (Exception e) {
                logger.warn("Failed to fetch Agent Leads from Account Service: {}", e.getMessage());
            }

            // No Agent Lead available
            if (agentLeadId == null) {
                throw new RuntimeException("No Agent Leads available for escalation. Please ensure Account Service is running and has active Agent Leads.");
            }

            // Update complaint assignment and status
            complaint.setAssignee(agentLeadId);
            ComplaintEntity updatedComplaint = updateStatus(complaintId, userId,
                Enums.ComplaintStatus.OPEN, Enums.ComplaintState.ESCALATED);

            // Send notification to Agent Lead
            try {
                // TODO: Implement notification to Agent Lead about escalated complaint
                logger.info("Notification should be sent to Agent Lead: {}", agentLeadId);
            } catch (Exception e) {
                logger.warn("Failed to send notification to Agent Lead: {}", e.getMessage());
            }

            logger.info("Complaint {} escalated to Agent Lead: {}", complaintId, agentLeadId);
            return updatedComplaint;
        } catch (Exception e) {
            logger.error("Error escalating complaint: {}", e.getMessage());
            throw e;
        }
    }



    /**
     * Close complaint with notifications
     */
    public ComplaintEntity closeComplaint(String complaintId, String userId) {
        try {
            ComplaintEntity updatedComplaint = updateStatus(complaintId, userId,
                Enums.ComplaintStatus.CLOSED, Enums.ComplaintState.COMPLETED);

            // Send closure notifications (In-app + Email)
            try {
                // TODO: Implement notification to complaint creator about closure
                logger.info("Notification should be sent to complaint creator about closure");
            } catch (Exception e) {
                logger.warn("Failed to send closure notification: {}", e.getMessage());
            }

            logger.info("Complaint {} closed successfully", complaintId);
            return updatedComplaint;
        } catch (Exception e) {
            logger.error("Error closing complaint: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * ✅ ROLE-BASED FILTERING SPECIFICATION
     * Exact match with pre-approval service pattern
     */
    private Specification<ComplaintEntity> buildRoleBasedSpecification(String role, String userId) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            logger.info("Building role-based specification for role: {} and userId: {}", role, userId);

            switch (role.toUpperCase()) {
                case "AGENT":
                    if (userId != null) {
                        // ✅ AGENT with userId: sees only SUBMITTED complaints assigned to them
                        predicates.add(criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("assignee"), userId),
                            criteriaBuilder.equal(root.get("state"), Enums.ComplaintState.SUBMITTED)
                        ));
                        logger.info("AGENT filter (with userId): assignee={}, state=SUBMITTED", userId);
                    } else {
                        // ✅ AGENT without userId: sees ALL SUBMITTED complaints (regardless of assignee)
                        predicates.add(criteriaBuilder.equal(root.get("state"), Enums.ComplaintState.SUBMITTED));
                        logger.info("AGENT filter (without userId): state=SUBMITTED (all submitted complaints)");
                    }
                    break;

                case "AGENT_LEAD":
                    if (userId != null) {
                        // ✅ AGENT_LEAD with userId: sees only ESCALATED complaints assigned to them
                        predicates.add(criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("assignee"), userId),
                            criteriaBuilder.equal(root.get("state"), Enums.ComplaintState.ESCALATED)
                        ));
                        logger.info("AGENT_LEAD filter (with userId): assignee={}, state=ESCALATED", userId);
                    } else {
                        // ✅ AGENT_LEAD without userId: sees ALL ESCALATED complaints (regardless of assignee)
                        predicates.add(criteriaBuilder.equal(root.get("state"), Enums.ComplaintState.ESCALATED));
                        logger.info("AGENT_LEAD filter (without userId): state=ESCALATED (all escalated complaints)");
                    }
                    break;

                default:
                    // For unknown roles, return no results
                    predicates.add(criteriaBuilder.equal(criteriaBuilder.literal(1), 0));
                    logger.warn("Unknown role: {}, returning no results", role);
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}