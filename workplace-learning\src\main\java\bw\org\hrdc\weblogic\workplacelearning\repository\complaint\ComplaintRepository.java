package bw.org.hrdc.weblogic.workplacelearning.repository.complaint;

import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 22:04
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 22:04
 */
@Repository
public interface ComplaintRepository extends JpaRepository<ComplaintEntity, UUID>, JpaSpecificationExecutor<ComplaintEntity> {
    @Query("SELECT o FROM ComplaintEntity o WHERE o.uuid = :uuid")
    Optional<ComplaintEntity> findByUuid(String uuid);

    @Query("SELECT o FROM ComplaintEntity o WHERE o.organisationId = :companyId")
    Page<ComplaintEntity> findAllByCompany(String companyId, Pageable pageable);

    @Query(value = """
        SELECT\s
            COUNT(c.id) AS TOTAL_COMPLAINTS,
            COUNT(c.id) FILTER (WHERE c.status = 'OPEN' AND c.created_at >= (CURRENT_TIMESTAMP - INTERVAL '3 days')) AS OPEN_COMPLAINTS,
            COUNT(c.id) FILTER (WHERE c.status IN ('IN_PROGRESS', 'AWAITING_CLIENT_FEEDBACK', 'ESCALATED') AND c.created_at >= (CURRENT_TIMESTAMP - INTERVAL '3 days')) AS IN_PROGRESS_COMPLAINTS,
            COUNT(c.id) FILTER (WHERE c.status = 'CLOSED') AS CLOSED_COMPLAINTS,
            COUNT(c.id) FILTER (WHERE c.created_at < (CURRENT_TIMESTAMP - INTERVAL '3 days') AND c.status != 'CLOSED') AS OVERDUE_COMPLAINTS
        FROM complaints c
        WHERE c.organisation_id = :companyId AND (:category IS NULL OR c.category = :category)
    \s""", nativeQuery = true)
    List<Object[]> getCompanyComplaintStatistics(@Param("companyId") String companyId, @Param("category") String category);

    @Query(value = """
        SELECT\s
            COUNT(c.id) AS TOTAL_COMPLAINTS,
            COUNT(c.id) FILTER (WHERE c.status = 'OPEN') AS OPEN_COMPLAINTS,
            COUNT(c.id) FILTER (WHERE c.status IN ('IN_PROGRESS', 'AWAITING_CLIENT_FEEDBACK', 'ESCALATED')) AS IN_PROGRESS_COMPLAINTS,
            COUNT(c.id) FILTER (WHERE c.status = 'CLOSED') AS CLOSED_COMPLAINTS,
            COUNT(c.id) FILTER (WHERE c.created_at < (CURRENT_TIMESTAMP - INTERVAL '3 days') AND c.status != 'CLOSED') AS OVERDUE_COMPLAINTS
        FROM complaints c WHERE (:category IS NULL OR c.category = :category)
    \s""", nativeQuery = true)
    List<Object[]> getComplaintStatistics(@Param("category") String category);

    
    @Query(value = """
        SELECT assignee, COUNT(*) as complaint_count
        FROM complaints
        WHERE state != 'COMPLETED'
          AND deleted = false
          AND category = 'COMPLAINT'
          AND assignee IS NOT NULL
        GROUP BY assignee
        ORDER BY complaint_count ASC
        """, nativeQuery = true)
    List<Object[]> getComplaintCountByAgent();

    
    @Query(value = """
        SELECT assignee, COUNT(*) as complaint_count
        FROM complaints
        WHERE state = 'ESCALATED'
          AND status != 'CLOSED'
          AND deleted = false
          AND category = 'COMPLAINT'
          AND assignee IS NOT NULL
        GROUP BY assignee
        ORDER BY complaint_count ASC
        """, nativeQuery = true)
    List<Object[]> getEscalatedComplaintCountByAgentLead();

    /**
     *  ADD: Manager workload query for appeals
     */
    @Query(value = """
        SELECT assignee, COUNT(*) as appeal_count
        FROM complaints
        WHERE status IN ('OPEN', 'IN_PROGRESS', 'AWAITING_CLIENT_FEEDBACK')
          AND deleted = false
          AND category = 'APPEAL'
          AND assignee IS NOT NULL
        GROUP BY assignee
        ORDER BY appeal_count ASC
        """, nativeQuery = true)
    List<Object[]> getAppealCountByManager();
}

