package bw.org.hrdc.weblogic.workplacelearning.controller.complaint;

import bw.org.hrdc.weblogic.workplacelearning.api.WorkflowClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.*;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintDocument;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.service.NotificationService;
import bw.org.hrdc.weblogic.workplacelearning.service.complaint.ComplaintService;
import bw.org.hrdc.weblogic.workplacelearning.service.complaint.PaperlessService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.inject.Inject;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import static bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse.getInternalServerError;

/**
 * <AUTHOR>
 * @CreatedOn 27/03/25 22:24
 * @UpdatedBy martinspectre
 * @UpdatedOn 27/03/25 22:24
 */
@RestController
@RequestMapping("/api/v1/complaints")
@RequiredArgsConstructor
public class ComplaintController {

    private static final Logger logger = LoggerFactory.getLogger(ComplaintController.class);

    @Inject
    private final ComplaintService complaintService;
    @Inject
    private final PaperlessService paperlessService;

    @Inject
    private final WorkflowClient workflowClient;

    @Inject
    private final NotificationService notificationService;

    @PostMapping
    public ResponseEntity<?> createComplaint(@RequestBody() RequestPayload payload, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Complaint create initiated by: {}", userId);
        try {
            ComplaintEntity savedComplaint = complaintService.createComplaint(payload.getDetails(), userId);
            if(savedComplaint != null){
                if (payload.getDocuments() != null) {
                    List<ComplaintDocument> documents = payload.getDocuments();
                    for (ComplaintDocument document : documents) {
                        document.setComplaint(savedComplaint);
                        complaintService.createComplaintDocument(document);
                    }
                }
                Optional<ComplaintEntity> response = Optional.ofNullable(complaintService.fetchById(savedComplaint.getUuid()));
                //TODO Send an email to client contact about the created complaint with reference time and resolution time
                System.out.println("Complaint created with reference number: " + savedComplaint.getReferenceNumber());
                try {
                    Map<String, Object> workflowResponse = workflowClient.getComplaintsWorkflow(savedComplaint.getUuid());

                    System.out.println("Workflow Response: " + workflowResponse);
                    logger.info("Workflow response: {}", workflowResponse);
                    
                    if (workflowResponse != null && Boolean.TRUE.equals(workflowResponse.get("success"))) {
                        // Extract processInstanceId directly from the workflow response
                        String processInstanceId = (String) workflowResponse.get("processInstanceId");
                        String preApprovalId = response.get().getUuid();

                        if (processInstanceId != null && preApprovalId != null) {
                            System.out.println("Process Instance ID: " + processInstanceId);
                            System.out.println("preApprovalId: " + preApprovalId);
                            
                            // Save the processInstanceId to the database
                            complaintService.updateProcessInstanceIdToPreApprovalApplication(preApprovalId, processInstanceId);
                            response.get().setProcessInstanceId(processInstanceId);
                            logger.info("Process instance ID {} saved for application {}", processInstanceId, preApprovalId);
                        } else {
                            logger.warn("Missing processInstanceId or preApprovalId - processInstanceId: {}, preApprovalId: {}", 
                                      processInstanceId, preApprovalId);
                        }
                    } else {
                        logger.warn("Workflow response was unsuccessful or null");
                    }
                    
                } catch (Exception e) {
                    // TODO: handle exception
                }
                return ResponseEntity.ok(new ApiResponse<>(true, "complaint created successfully", response.orElse(null), null));
            }else {
                logger.error("Complaint create failed");
                return ApiResponse.createErrorResponse("COMPLAINT_FILING_ERROR", "Failed to create complaint.");
            }
        } catch (Exception e) {
            logger.error("Complaint creation failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> getAllComplaints(@PathVariable Integer offset, @PathVariable Integer pageSize) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Page<ListData> applications = complaintService.fetchList(PageRequest.of(offset, pageSize));

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<?>> getComplaintById(@PathVariable String id) {
        try {
            ComplaintResponse complaint = complaintService.fetchDetailedComplaint(id);
            if(complaint != null){
                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", complaint, null));

            }else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "No record found", null, null));

            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<?> updateStatus(@PathVariable String id, @RequestBody ComplaintStatusUpdatePayload request, @RequestHeader("X-Authenticated-User") String userId) {

        try {
            ComplaintEntity complaint = complaintService.updateStatus(id, userId, request.getStatus(), request.getState());
            if(complaint != null){
                //TODO send email if the complain is closed or escalated
                return ResponseEntity.ok(new ApiResponse<>(true, "Complaint updated", complaint, null));

            }else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Error updating complaint status", null, null));

            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{offset}/{pageSize}/{companyId}")
    public ResponseEntity<ApiResponse<?>> getAllCompanyComplaints(@PathVariable Integer offset, @PathVariable Integer pageSize, @PathVariable String companyId) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Sort sort = Sort.by("status").descending();
            Pageable pageable = PageRequest.of(offset, pageSize, sort);

            Page<ListData> applications = complaintService.fetchCompanyList(companyId, pageable);

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{companyId}/statistics")
    public ResponseEntity<?> getCompanyStatistics(@PathVariable String companyId) {
        try {
            ComplaintsStats statistics =  complaintService.getCompanyStatistics(companyId);
            return ResponseEntity.ok(new ApiResponse<>(true, "Statistical data", statistics, null));
        } catch (Exception e) {
            logger.error("Failed to fetch company statistical data with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/statistics")
    public ResponseEntity<?> getAllStatistics() {
        try {
            ComplaintsStats statistics =  complaintService.getStatistics();
            return ResponseEntity.ok(new ApiResponse<>(true, "Statistical data", statistics, null));
        } catch (Exception e) {
            logger.error("Failed to fetch statistical data with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PostMapping("/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> getAllUserAssignedComplaints(@PathVariable Integer offset, @PathVariable Integer pageSize, @RequestBody ComplaintSearchCriteria searchCriteria) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Page<ListData> applications = complaintService.fetchUserAssignedList(PageRequest.of(offset, pageSize), searchCriteria);

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/fetch")
    public ResponseEntity<ApiResponse<?>> getComplaintsByRole(
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String companyId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String assignedTo,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "0") int pageNumber,
            @RequestParam(defaultValue = "10") int size) {

        logger.info("Fetching complaints with criteria - role: {}, userId: {}, companyId: {}, status: {}, state: {}, assignedTo: {}, search: {}",
                role, userId, companyId, status, state, assignedTo, search);

        try {
            if (size <= 0) {
                size = 10;
            }

            // Build search criteria from query parameters
            ComplaintSearchCriteria searchCriteria = new ComplaintSearchCriteria();
            searchCriteria.setRole(role);
            searchCriteria.setAssignedTo(userId); // Use userId for role-based filtering
            searchCriteria.setStatus(status);

            logger.info("Role-based filtering: role={}, userId={}", role, userId);

            Page<ListData> applications = complaintService.fetchUserAssignedList(PageRequest.of(pageNumber, size), searchCriteria);

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            logger.error("Failed to fetch complaints with exception: ", e);
            return getInternalServerError(e.getMessage());
        }
    }

    @PatchMapping("/{id}/assign-agent/{agentId}")
    public ResponseEntity<?> assignAgent(@PathVariable String id, @PathVariable String agentId, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Complaint agent assignment initiated for complaint id : {}", id);
        try {
            ComplaintEntity complaint = complaintService.fetchById(id);
            if(complaint != null){
                complaint.setAssignee(agentId);
                ComplaintEntity updateStatus = complaintService.assignedNewAssistance(id, agentId, userId);
                if (updateStatus == null) {
                    logger.error("Failed to assign complaint {} to agent", id);
                    return ApiResponse.createErrorResponse("COMPLIANT_UPDATE_ERROR", "Failed to process complaint action.");
                }else{
                    //TODO trigger notification to agent about the reassignment
                    //TODO trigger notification to agent for the assignment made
                    notificationService.sendNotificationToUser(
                                    agentId,
                                    complaint.getReferenceNumber(),
                                    complaint.getUuid(),
                                    complaint.getStatus().name(),
                                    Enums.NotificationType.IN_APP.name(),
                                    Enums.ApplicationType.COMPLAINTS.name()
                                    
                    );

                    return ResponseEntity.ok(new ApiResponse<>(true, "Agent assigned successfully", null, null));
                }
            }else{
                return ApiResponse.createErrorResponse("COMPLIANT_NOT_FOUND", "Provided complaint id does not exist");
            }
        } catch (Exception e) {
            logger.error("Failed to assign complaint id {} to agent id {} with exception: {}", id, agentId, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * Agent/Agent Lead communicates with client
     */
    @PatchMapping("/{id}/communicate")
    public ResponseEntity<?> communicateWithClient(@PathVariable String id, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Agent communication initiated for complaint id: {}", id);
        try {
            ComplaintEntity complaint = complaintService.fetchById(id);
            if (complaint != null) {
                Enums.ComplaintState newState = complaint.getState() == Enums.ComplaintState.ESCALATED ?
                    Enums.ComplaintState.ESCALATED : Enums.ComplaintState.UNDER_REVIEW;

                ComplaintEntity updatedComplaint = complaintService.updateStatus(id, userId,
                    Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK, newState);

                return ResponseEntity.ok(new ApiResponse<>(true, "Communication initiated with client", updatedComplaint, null));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "Complaint not found", null, null));
            }
        } catch (Exception e) {
            logger.error("Failed to initiate communication for complaint id {} with exception: {}", id, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * Agent escalates complaint to Agent Lead
     */
    @PatchMapping("/{id}/escalate")
    public ResponseEntity<?> escalateComplaint(@PathVariable String id, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Complaint escalation initiated for complaint id: {}", id);
        try {
            ComplaintEntity updatedComplaint = complaintService.escalateToAgentLead(id, userId);
            if (updatedComplaint != null) {
                return ResponseEntity.ok(new ApiResponse<>(true, "Complaint escalated to Agent Lead", updatedComplaint, null));
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(new ApiResponse<>(false, "Failed to escalate complaint", null, null));
            }
        } catch (Exception e) {
            logger.error("Failed to escalate complaint id {} with exception: {}", id, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * Agent/Agent Lead closes complaint
     */
    @PatchMapping("/{id}/close")
    public ResponseEntity<?> closeComplaint(@PathVariable String id, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Complaint closure initiated for complaint id: {}", id);
        try {
            ComplaintEntity updatedComplaint = complaintService.closeComplaint(id, userId);
            if (updatedComplaint != null) {
                return ResponseEntity.ok(new ApiResponse<>(true, "Complaint closed successfully", updatedComplaint, null));
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(new ApiResponse<>(false, "Failed to close complaint", null, null));
            }
        } catch (Exception e) {
            logger.error("Failed to close complaint id {} with exception: {}", id, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * Client responds to Agent/Agent Lead
     */
    @PatchMapping("/{id}/client-response")
    public ResponseEntity<?> clientResponse(@PathVariable String id, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Client response recorded for complaint id: {}", id);
        try {
            ComplaintEntity complaint = complaintService.fetchById(id);
            if (complaint != null) {
                Enums.ComplaintState newState = complaint.getState() == Enums.ComplaintState.ESCALATED ?
                    Enums.ComplaintState.ESCALATED : Enums.ComplaintState.UNDER_REVIEW;

                ComplaintEntity updatedComplaint = complaintService.updateStatus(id, userId,
                    Enums.ComplaintStatus.IN_PROGRESS, newState);

                return ResponseEntity.ok(new ApiResponse<>(true, "Client response recorded", updatedComplaint, null));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "Complaint not found", null, null));
            }
        } catch (Exception e) {
            logger.error("Failed to record client response for complaint id {} with exception: {}", id, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }
}
