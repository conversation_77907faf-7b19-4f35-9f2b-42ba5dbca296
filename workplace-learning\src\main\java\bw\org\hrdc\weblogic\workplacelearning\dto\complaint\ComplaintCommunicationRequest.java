package bw.org.hrdc.weblogic.workplacelearning.dto.complaint;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * DTO for complaint communication request
 * Used when agents communicate with clients or clients respond
 * 
 * <AUTHOR>
 * @CreatedOn 17/06/25 15:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintCommunicationRequest {

    @NotBlank(message = "Message cannot be empty")
    @Size(min = 10, max = 2000, message = "Message must be between 10 and 2000 characters")
    private String message;

    private String messageType; // COMMUNICATION, RESPONSE (optional, will be determined by endpoint)
}
