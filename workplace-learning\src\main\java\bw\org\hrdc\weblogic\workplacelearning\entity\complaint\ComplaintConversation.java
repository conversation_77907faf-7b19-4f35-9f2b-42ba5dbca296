package bw.org.hrdc.weblogic.workplacelearning.entity.complaint;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity for storing conversation messages between agents and clients
 * for complaint communication
 * 
 * <AUTHOR>
 * @CreatedOn 17/06/25 15:30
 */
@Entity
@Table(name = "complaint_conversation")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintConversation {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    private UUID id;

    @Column(name = "complaint_id", nullable = false)
    private String complaintId;

    @Column(name = "message", nullable = false, columnDefinition = "TEXT")
    private String message;

    @Column(name = "from_user", nullable = false)
    private String fromUser;

    @Column(name = "to_user", nullable = false)
    private String toUser;

    @Column(name = "from_role", nullable = false)
    private String fromRole; // AGENT, AGENT_LEAD, CLIENT

    @Column(name = "to_role", nullable = false)
    private String toRole; // AGENT, AGENT_LEAD, CLIENT

    @Column(name = "message_type", nullable = false)
    private String messageType; // COMMUNICATION, RESPONSE

    @Column(name = "is_read", nullable = false)
    private Boolean isRead = false;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "deleted", nullable = false)
    private Boolean deleted = false;

    // Convenience constructor for creating messages
    public ComplaintConversation(String complaintId, String message, String fromUser, String toUser, 
                               String fromRole, String toRole, String messageType, String createdBy) {
        this.complaintId = complaintId;
        this.message = message;
        this.fromUser = fromUser;
        this.toUser = toUser;
        this.fromRole = fromRole;
        this.toRole = toRole;
        this.messageType = messageType;
        this.createdBy = createdBy;
        this.isRead = false;
        this.deleted = false;
    }
}
