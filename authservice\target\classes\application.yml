server:
  port: 3452
spring:
  application:
    name: auth-service
  profiles:
    active: local
  datasource:
    url: *******************************************
    username: postgres
    password: root
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      pool-name: HikariCP
      idle-timeout: 30000
      max-lifetime: 60000
      connection-timeout: 30000
  jpa:
    hibernate:
      ddl-auto: update  #'none', 'validate', 'update', 'create', or 'create-drop'
    show-sql: true  # Set to true to log the SQL queries to the console
  # Security Configuration
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: ehrdf
            client-secret: 1ariTRLyhqL1i8REEwlhQlJT2lQgkbKd
            authorization-grant-type: authorization_code
            scope: openid,profile,email
        provider:
          keycloak:
            issuer-uri: https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc
      resourceserver:
        jwt:
          issuer-uri: https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc
          jwk-set-uri: https://hrdcdev.weblogic.co.bw/keycloak/realms/hrdc/protocol/openid-connect/certs
jwt:
  auth:
    converter:
      resource-id: ehrdf
      principal-attribute: principal_username

keycloak:
  server-url: https://hrdcdev.weblogic.co.bw/keycloak
  realm: hrdc
  client-id: ehrdf
  client-secret: 1ariTRLyhqL1i8REEwlhQlJT2lQgkbKd
  admin-username: admin
  admin-password: admin
  protocol: http

eureka:
  instance:
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 5   # Reduce interval to avoid expiration issues
    leaseExpirationDurationInSeconds: 10 # Reduce expiration timeout
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: http://localhost:8070/eureka/
    healthcheck:
      enabled: true

otp-service:
  url: http://otp-service

feign:
  client:
    defaultToProperties: true
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000
  circuitbreaker:
    enabled: true

logging:
  level:
    org.springframework.security: INFO
    web: INFO
    org:
      hibernate:
        SQL=DEBUG: DEBUG