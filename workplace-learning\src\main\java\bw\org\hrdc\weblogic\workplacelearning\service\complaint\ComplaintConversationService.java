package bw.org.hrdc.weblogic.workplacelearning.service.complaint;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintConversationResponse;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintConversation;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintConversationRepository;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service for managing complaint conversations
 * 
 * <AUTHOR>
 * @CreatedOn 17/06/25 15:50
 */
@Service
@RequiredArgsConstructor
public class ComplaintConversationService {

    private static final Logger logger = LoggerFactory.getLogger(ComplaintConversationService.class);

    private final ComplaintConversationRepository conversationRepository;
    private final ComplaintService complaintService;
    private final CompanyClient companyClient;

    /**
     * Create a new conversation message
     */
    @Transactional
    public ComplaintConversation createMessage(String complaintId, String message, String fromUser, 
                                             String toUser, String fromRole, String toRole, 
                                             String messageType, String createdBy) {
        logger.info("Creating conversation message for complaint: {}", complaintId);
        
        ComplaintConversation conversation = new ComplaintConversation(
            complaintId, message, fromUser, toUser, fromRole, toRole, messageType, createdBy
        );
        
        ComplaintConversation savedConversation = conversationRepository.save(conversation);
        logger.info("Conversation message created with ID: {}", savedConversation.getId());
        
        return savedConversation;
    }

    /**
     * Get all conversations for a complaint
     */
    public List<ComplaintConversationResponse> getComplaintConversations(String complaintId, String currentUserId) {
        logger.info("Fetching conversations for complaint: {}", complaintId);
        
        List<ComplaintConversation> conversations = conversationRepository.findByComplaintIdOrderByCreatedAtAsc(complaintId);
        
        return conversations.stream()
            .map(conv -> mapToResponse(conv, currentUserId))
            .collect(Collectors.toList());
    }

    /**
     * Get conversations with pagination
     */
    public Page<ComplaintConversationResponse> getComplaintConversations(String complaintId, String currentUserId, Pageable pageable) {
        logger.info("Fetching paginated conversations for complaint: {}", complaintId);
        
        Page<ComplaintConversation> conversations = conversationRepository.findByComplaintIdOrderByCreatedAtAsc(complaintId, pageable);
        
        return conversations.map(conv -> mapToResponse(conv, currentUserId));
    }

    /**
     * Mark messages as read for a user
     */
    @Transactional
    public void markMessagesAsRead(String complaintId, String userId) {
        logger.info("Marking messages as read for complaint: {} and user: {}", complaintId, userId);
        conversationRepository.markMessagesAsRead(complaintId, userId);
    }

    /**
     * Get unread message count for a user
     */
    public Long getUnreadMessageCount(String userId) {
        return conversationRepository.countUnreadMessagesByUser(userId);
    }

    /**
     * Get latest message for a complaint
     */
    public ComplaintConversationResponse getLatestMessage(String complaintId, String currentUserId) {
        ComplaintConversation latest = conversationRepository.findLatestMessageByComplaintId(complaintId);
        return latest != null ? mapToResponse(latest, currentUserId) : null;
    }

    /**
     * Create agent communication message
     */
    @Transactional
    public ComplaintConversation createAgentCommunication(String complaintId, String message, String agentId, String clientId) {
        logger.info("Creating agent communication for complaint: {}", complaintId);
        
        // Determine agent role based on complaint state
        ComplaintEntity complaint = complaintService.fetchById(complaintId);
        String agentRole = complaint.getState().name().equals("ESCALATED") ? "AGENT_LEAD" : "AGENT";
        
        return createMessage(complaintId, message, agentId, clientId, agentRole, "CLIENT", "COMMUNICATION", agentId);
    }

    /**
     * Create client response message
     */
    @Transactional
    public ComplaintConversation createClientResponse(String complaintId, String message, String clientId, String agentId) {
        logger.info("Creating client response for complaint: {}", complaintId);
        
        // Determine agent role based on complaint state
        ComplaintEntity complaint = complaintService.fetchById(complaintId);
        String agentRole = complaint.getState().name().equals("ESCALATED") ? "AGENT_LEAD" : "AGENT";
        
        return createMessage(complaintId, message, clientId, agentId, "CLIENT", agentRole, "RESPONSE", clientId);
    }

    /**
     * Map conversation entity to response DTO
     */
    private ComplaintConversationResponse mapToResponse(ComplaintConversation conversation, String currentUserId) {
        ComplaintConversationResponse response = new ComplaintConversationResponse();
        response.setId(conversation.getId());
        response.setComplaintId(conversation.getComplaintId());
        response.setMessage(conversation.getMessage());
        response.setFromUser(conversation.getFromUser());
        response.setToUser(conversation.getToUser());
        response.setFromRole(conversation.getFromRole());
        response.setToRole(conversation.getToRole());
        response.setMessageType(conversation.getMessageType());
        response.setIsRead(conversation.getIsRead());
        response.setCreatedAt(conversation.getCreatedAt());
        response.setUpdatedAt(conversation.getUpdatedAt());
        response.setIsCurrentUser(conversation.getFromUser().equals(currentUserId));
        
        // Try to get user names (optional enhancement)
        try {
            response.setFromUserName(getUserName(conversation.getFromUser()));
            response.setToUserName(getUserName(conversation.getToUser()));
        } catch (Exception e) {
            logger.warn("Failed to fetch user names: {}", e.getMessage());
            response.setFromUserName("Unknown User");
            response.setToUserName("Unknown User");
        }
        
        return response;
    }

    /**
     * Get user name by ID (helper method)
     */
    private String getUserName(String userId) {
        try {
            ApiResponse<?> userResponse = companyClient.fetchUserById(userId);
            if (userResponse.isStatus() && userResponse.getData() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> userData = (Map<String, Object>) userResponse.getData();
                String firstName = (String) userData.get("firstName");
                String lastName = (String) userData.get("lastName");
                return (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
            }
        } catch (Exception e) {
            logger.debug("Failed to fetch user name for ID: {}", userId);
        }
        return "Unknown User";
    }
}
