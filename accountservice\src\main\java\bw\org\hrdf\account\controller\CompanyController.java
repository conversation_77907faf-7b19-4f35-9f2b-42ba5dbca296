package bw.org.hrdf.account.controller;

import bw.org.hrdf.account.dto.CompanyDTO;
import bw.org.hrdf.account.dto.CompanyListDTO;
import bw.org.hrdf.account.dto.CompanyStatisticsDTO;
import bw.org.hrdf.account.dto.EtpListDTO;
import bw.org.hrdf.account.entity.company.*;
import bw.org.hrdf.account.service.company.*;
import bw.org.hrdf.account.entity.enums.VerificationTypeEnum;
import bw.org.hrdf.account.helper.ApiResponse;
import bw.org.hrdf.account.helper.BaseSpecifications;
import bw.org.hrdf.account.service.AccountService;
import bw.org.hrdf.account.models.company.CompanyRequest;
import bw.org.hrdf.account.models.company.CompanySearchCriteria;
import bw.org.hrdf.account.models.company.ContactPersonModel;
import bw.org.hrdf.account.models.company.VerificationModel;
import bw.org.hrdf.account.repositories.company.CompanyRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static bw.org.hrdf.account.helper.ApiResponse.getInternalServerError;

@RestController
@RequestMapping("/api/v1/company")
public class CompanyController {

    private static final Logger logger = LoggerFactory.getLogger(CompanyController.class);

    @Autowired
    private CompanyService companyService;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private VerificationService verificationService;

    @Autowired
    private ContactPersonService contactPersonService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private CompanyRepository companyRepository;

    @PostMapping("/new/{userId}")
    public ResponseEntity<?> createCompany(@PathVariable String userId, @RequestBody CompanyRequest companyRequest) {
        logger.info("Company creation initiated by: {}", userId);
        try {
            List<VerificationModel> verifications = companyRequest.getVerifications();

            Map<VerificationTypeEnum, String> verificationReferences = companyService.getVerificationReferences(verifications);
            if (verificationReferences.isEmpty()) {
                return ApiResponse.createErrorResponse("VERIFICATION_REQUIRED", "No valid verification references provided.");
            }

            for (Map.Entry<VerificationTypeEnum, String> entry : verificationReferences.entrySet()) {
                if (companyService.isCompanyReferenceExists(entry.getValue(), entry.getKey())) {
                    return ApiResponse.createErrorResponse(
                            entry.getKey() + "_EXIST",
                            String.format("Provided %s already exists.", entry.getKey())
                    );
                }
            }

            CompanyEntity company = companyService.buildCompanyEntity(companyRequest);
            if (company == null) {
                return ApiResponse.createErrorResponse("INVALID_COMPANY_DATA", "Company data is invalid.");
            }

            CompanyEntity savedCompany = companyService.createCompany(company);
            if (savedCompany == null) {
                return ApiResponse.createErrorResponse("UNKNOWN_ERROR", "Failed to create company.");
            }
            logger.info("New company of id {} created successfully", savedCompany.getUuid());

            companyService.processContactPerson(companyRequest.getContactPerson(), savedCompany);

            logger.info("New company id {} initiating application entry", savedCompany.getUuid());
            ApplicationEntity savedApplication = companyService.processApplication(companyRequest, savedCompany, verifications);

            if (savedApplication == null) {
                logger.error("New company registration id {} failed to generate new application for registration", savedCompany.getUuid());
                return ApiResponse.createErrorResponse("APPLICATION_ERROR", "Failed to process application.");
            }

            companyService.processVerifications(savedCompany, verifications, savedApplication);

            accountService.createUserCompany(userId, savedCompany.getUuid());

            Optional<CompanyDTO> response = companyService.getCompanyByUuid(savedCompany.getUuid());
            //TODO Trigger email/sms and inapp notification about the newly created company
            return ResponseEntity.ok(new ApiResponse<>(true, "Company created successfully", response.orElse(null), null));

        } catch (Exception e) {
            logger.error("Company creation failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PostMapping("/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> getAllOrganisations(@PathVariable Integer offset, @PathVariable Integer pageSize,
            @RequestBody CompanySearchCriteria searchCriteria) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Sort.Direction sortDirection = Sort.Direction.fromString(searchCriteria.getDirection().toUpperCase());
            Sort sort = Sort.by(sortDirection, searchCriteria.getSortBy());

            Page<CompanyListDTO> companies = companyService.getAllCompanies(
                    searchCriteria, PageRequest.of(offset, pageSize, sort));

            if (companies.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", companies.getNumber());
            metadata.put("totalPages", companies.getTotalPages());
            metadata.put("totalElements", companies.getTotalElements());
            metadata.put("pageSize", companies.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", companies.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<?>> getCompanyById(@PathVariable String id) {
        try {
            Optional<CompanyDTO> company = companyService.getCompanyByUuid(id);

            return company.<ResponseEntity<ApiResponse<?>>>map(companyDTO -> ResponseEntity.ok(new ApiResponse<>(true, "Record found", companyDTO, null))).orElseGet(() -> ResponseEntity.status(HttpStatus.OK)
                    .body(new ApiResponse<>(false, "No record found", null, null)));

        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateOrganisation(@PathVariable String id, @RequestBody CompanyEntity companyDetails) {
        try {
            Optional<CompanyEntity> savedCompany = companyService.getCompanyById(id);
            if (savedCompany.isPresent()) {
                CompanyEntity existingCompany = getCompanyEntity(companyDetails, savedCompany.get());

                String updateResult = companyService.updateCompany(existingCompany);
                if(updateResult.equals("SUCCESSFUL")){

                    ApplicationEntity application = companyDetails.getRegistrationApplication();

                    ContactPerson contactPerson = companyDetails.getContactPerson();

                    applicationService.updateApplicationStatus(application.getReferenceNumber(), application.getState(), application.getStatus());

                    contactPersonService.update(contactPerson);

                    return ResponseEntity.status(HttpStatus.OK)
                            .body(new ApiResponse<>(true, "Update successful", null,null));
                }
                return ApiResponse.createErrorResponse(updateResult, "Company details failed to update");
            } else {
                return ApiResponse.createErrorResponse("COMPANY_NOT_FOUND", "Provided company identifier does not exist");
            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    private static CompanyEntity getCompanyEntity(CompanyEntity companyDetails, CompanyEntity existingCompany) {
        existingCompany.setName(companyDetails.getName());
        existingCompany.setType(companyDetails.getType());
        existingCompany.setIndustry(companyDetails.getIndustry());
        existingCompany.setCategory(companyDetails.getCategory());
        existingCompany.setPhysicalAddress(companyDetails.getPhysicalAddress());
        existingCompany.setTelephoneNumber(companyDetails.getTelephoneNumber());
        existingCompany.setFaxNumber(companyDetails.getFaxNumber());
        existingCompany.setPepPipStatus(companyDetails.isPepPipAssociateStatus());
        existingCompany.setPepPipStatus(companyDetails.isPepPipStatus());
        return existingCompany;
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteCompany(@PathVariable String id) {
        try {
            if (companyService.getCompanyByUuid(id).isPresent()) {
                String updateResult = companyService.deleteCompany(id);
                if(updateResult.equals("SUCCESSFUL")){
                    //TODO Trigger email and/or sms notification to the company owner about company removal
                    return ResponseEntity.status(HttpStatus.OK)
                            .body(new ApiResponse<>(true, "Company removed successful", null,null));
                }
                return ApiResponse.createErrorResponse(updateResult, "Company details failed to be removed");

            } else {
                return ApiResponse.createErrorResponse("COMPANY_NOT_FOUND", "Provided company identifier does not exist");
            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PostMapping("/{companyId}/contact-person/{userId}")
    public ResponseEntity<?> addContactPerson(@PathVariable String userId, @PathVariable String companyId, @RequestBody ContactPersonModel contactPersonRequest) {
        logger.info("Add contact person initiated by: {}", userId);
        try {
            Optional<CompanyEntity> savedCompany = companyService.getCompanyById(companyId);
            if (savedCompany.isPresent()) {
                CompanyEntity existingCompany = savedCompany.get();
                ContactPerson contactPerson = new ContactPerson();
                contactPerson.setCompany(existingCompany);
                contactPerson.setName(contactPersonRequest.getName());
                contactPerson.setPosition(contactPersonRequest.getPosition());
                ContactPerson savedContact = contactPersonService.save(contactPerson);
                if(savedContact != null){
                    //TODO Trigger notification to send to contact person informing them of the addition to the company
                    return ResponseEntity.status(HttpStatus.OK)
                            .body(new ApiResponse<>(true, "Add contact person successful", null,null));
                }
                return ApiResponse.createErrorResponse("CONTACT_PERSON_ADD_FAILURE", "Add contact person failed");
            } else {
                return ApiResponse.createErrorResponse("COMPANY_NOT_FOUND", "Provided company identifier does not exist");
            }
        } catch (Exception e) {
            logger.error("Add contact person failed with exception: {} for company id {} ", e.getMessage(), companyId);
            return getInternalServerError(e.getMessage());
        }
    }
    @PutMapping("/contact-person/{contactPersonId}")
    public ResponseEntity<?> updateContactPerson(@PathVariable String contactPersonId, @RequestBody ContactPersonModel contactPersonRequest) {
        try {
            ContactPerson existingContactPerson = contactPersonService.getContactPerson(contactPersonId);
            if (existingContactPerson != null) {
                existingContactPerson.setName(contactPersonRequest.getName());
                existingContactPerson.setPosition(contactPersonRequest.getPosition());
                String updateResult = contactPersonService.update(existingContactPerson);
                if(updateResult.equals("SUCCESSFUL")){
                    return ResponseEntity.status(HttpStatus.OK)
                            .body(new ApiResponse<>(true, "Update successful", null,null));
                }
                return ApiResponse.createErrorResponse(updateResult, "Contact person details failed to update");
            } else {
                return ApiResponse.createErrorResponse("USER_NOT_FOUND", "Provided contact person identifier does not exist");
            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }
    @PostMapping("/{companyId}/verifications")
    public ResponseEntity<?> addVerification(@PathVariable String companyId, @RequestBody VerificationModel verificationRequest) {
        logger.info("Add verification for company: {}", companyId);
        try {
            Optional<CompanyEntity> savedCompany = companyService.getCompanyById(companyId);
            if (savedCompany.isPresent()) {
                CompanyEntity existingCompany = savedCompany.get();
                if (companyService.isCompanyReferenceExists(verificationRequest.getReference(), verificationRequest.getType())) {
                    return ApiResponse.createErrorResponse(
                            verificationRequest.getType() + "_EXIST",
                            String.format("Provided %s already exists.", verificationRequest.getReference())
                    );
                }
                Verification verification = new Verification(
                        verificationRequest.getReference(),
                        verificationRequest.getType(),
                        verificationRequest.getStatus(),
                        verificationRequest.getVerifiedAt(),
                        verificationRequest.getExpiryDate(),
                        existingCompany
                );
                Optional<ApplicationEntity> fetchedApplication = applicationService.getApplicationByReferenceNumber(existingCompany.getRegistrationApplication().getReferenceNumber());
                String result;
                if (fetchedApplication.isPresent()) {
                    ApplicationEntity existingApplication = fetchedApplication.get();
                    result = companyService.processVerification(verification, existingApplication);
                }else{
                    result = companyService.processVerification(verification, null);
                }

                if(result.equals("SUCCESSFUL")){
                    return ResponseEntity.status(HttpStatus.OK)
                            .body(new ApiResponse<>(true, "Verification added successful", null,null));
                }
                return ApiResponse.createErrorResponse(result, "Verification details failed to be added");
            } else {
                return ApiResponse.createErrorResponse("COMPANY_NOT_FOUND", "Provided company identifier does not exist");
            }
        } catch (Exception e) {
            logger.error("Add verification failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }
    @PostMapping("/verifications/{verificationId}")
    public ResponseEntity<?> updateVerification(@PathVariable String verificationId, @RequestBody VerificationModel verificationRequest) {
        logger.info("Updating verification {}", verificationId);
        try {
            Optional<Verification> existingVerification = verificationService.getVerificationObject(verificationId);
            if (existingVerification.isPresent()) {
                Verification verification = existingVerification.get();
                verification.setStatus(verificationRequest.getStatus());
                verification.setReference(verificationRequest.getReference());
                verification.setType(verificationRequest.getType());
                verification.setVerifiedAt(verificationRequest.getVerifiedAt());
                verification.setExpiryDate(verificationRequest.getExpiryDate());

                Verification updateResult = verificationService.save(verification);
                if(updateResult != null){
                    //Update action.
                    return ResponseEntity.status(HttpStatus.OK)
                            .body(new ApiResponse<>(true, "Update successful", null,null));
                }
                return ApiResponse.createErrorResponse("VERIFICATION_UPDATE_FAILED", "Verification details failed to update");
            } else {
                return ApiResponse.createErrorResponse("GENERAL_NOT_FOUND", "Provided identifier does not exist");
            }
        } catch (Exception e) {
            logger.error("Update verification failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{companyId}/employees")
    public ResponseEntity<ApiResponse<?>> getCompanyEmployees(
            @PathVariable String companyId,
            @RequestParam(value = "offset", defaultValue = "0") Integer offset,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {

        Page<Map<String, Object>> employees = employeeService.findAllCompanyEmployees(companyId, PageRequest.of(offset, pageSize));
        if (employees.isEmpty()) {
            return ResponseEntity.status(HttpStatus.OK)
                    .body(new ApiResponse<>(false, "No records found", null, null));
        }

        return ResponseEntity.ok(new ApiResponse<>(true, "Records found", employees, null));
    }

    @PostMapping("/{companyId}/employees")
    public ResponseEntity<?> addEmployee(@PathVariable String companyId, @RequestBody Employee employee) {

        Optional<CompanyEntity> company = companyService.getCompanyById(companyId);
        if (company.isPresent()) {
            employee.setCompany(company.get());
            Employee savedEmployee = employeeService.save(employee);
            if(savedEmployee != null){
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(true, "Employee added successful", null,null));
            }
            return ApiResponse.createErrorResponse("EMPLOYEE_ADD_FAILURE", "Add employee failed");
        } else {
            return ApiResponse.createErrorResponse("COMPANY_NOT_FOUND", "Provided company identifier does not exist");
        }
    }
    @PostMapping("/{companyId}/employees/multi")
    public ResponseEntity<?> addEmployees(@PathVariable String companyId, @RequestBody Set<Employee> employees) {

        Optional<CompanyEntity> company = companyService.getCompanyById(companyId);
        if (company.isPresent()) {
            employees = employees.stream().peek(employee -> employee.setCompany(company.get())).collect(Collectors.toSet());
            List<Employee> savedEmployee = employeeService.saveList(employees);
            if(!savedEmployee.isEmpty()){
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(true, "Employees added successful", null,null));
            }
            return ApiResponse.createErrorResponse("EMPLOYEE_ADD_FAILURE", "Add employees failed");
        } else {
            return ApiResponse.createErrorResponse("COMPANY_NOT_FOUND", "Provided company identifier does not exist");
        }
    }
    @PutMapping("/employees/{employeeId}")
    public ResponseEntity<?> updateEmployee(@PathVariable String employeeId, @RequestBody Employee employee) {
        logger.info("Updating employee {}", employeeId);
        try {
            Optional<Employee> existingEmployee = employeeService.findById(employeeId);
            if (existingEmployee.isPresent()) {
                Employee employeeDetails = existingEmployee.get();
                employeeDetails.setIdType(employee.getIdType());
                employeeDetails.setIdNumber(employee.getIdNumber());
                employeeDetails.setIdVerificationStatus(employee.getIdVerificationStatus());
                employeeDetails.setFirstName(employee.getFirstName());
                employeeDetails.setLastName(employee.getLastName());
                employeeDetails.setGender(employee.getGender());
                employeeDetails.setContactNumber(employee.getContactNumber());
                employeeDetails.setBasicSalary(employee.getBasicSalary());

                Employee updateResult = employeeService.save(employeeDetails);

                if(updateResult != null){
                    return ResponseEntity.status(HttpStatus.OK)
                            .body(new ApiResponse<>(true, "Update successful", null,null));
                }
                return ApiResponse.createErrorResponse("EMPLOYEE_UPDATE_FAILED", "Employee details failed to update");
            } else {
                return ApiResponse.createErrorResponse("GENERAL_NOT_FOUND", "Provided identifier does not exist");
            }
        } catch (Exception e) {
            logger.error("Update employee failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }
    @DeleteMapping("/employees/{employeeId}")
    public ResponseEntity<?> deleteEmployee(@PathVariable String employeeId) {
        try {
            if (employeeService.findById(employeeId).isPresent()) {
                Optional<Employee> updateResult = employeeService.deleteById(employeeId);
                if(updateResult.isPresent() && updateResult.get().isDeleted()){
                    return ResponseEntity.status(HttpStatus.OK)
                            .body(new ApiResponse<>(true, "Employee removed successful", null,null));
                }
                return ApiResponse.createErrorResponse("EMPLOYEE_REMOVE_FAILURE", "Employee details failed to be removed");

            } else {
                return ApiResponse.createErrorResponse("EMPLOYEE_NOT_FOUND", "Provided employee identifier does not exist");
            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }
    @GetMapping("/employees/{employeeId}")
    public ResponseEntity<ApiResponse<?>> getEmployeeById(@PathVariable String employeeId) {
        try {
            Optional<Employee> employee = employeeService.findById(employeeId);

            return employee.<ResponseEntity<ApiResponse<?>>>map(emp -> ResponseEntity.ok(new ApiResponse<>(true, "Record found", emp, null))).orElseGet(() -> ResponseEntity.status(HttpStatus.OK)
                    .body(new ApiResponse<>(false, "No record found", null, null)));

        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/all")
    public ResponseEntity<ApiResponse<?>> getAllOrganisations() {
        try {
            List<CompanyListDTO> companies = companyService.getAllNonPageableCompanies();

            if (companies.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }
            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", companies, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/statistics")
    public ResponseEntity<?> getCompanyStatistics() {
        try {
            CompanyStatisticsDTO statistics =  companyService.getCompanyStatistics();
            return ResponseEntity.ok(new ApiResponse<>(true, "Statistical data", statistics, null));
        } catch (Exception e) {
            logger.error("Failed to fetch statistical data with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }

    }

    @GetMapping("/etps/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> fetchAllActiveEtps(@PathVariable Integer offset, @PathVariable Integer pageSize) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Page<EtpListDTO> companies = companyService.fetchAllActiveEtps(PageRequest.of(offset, pageSize));

            if (companies.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }
            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", companies, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<?>> searchCompanyByName(
            @RequestParam(value = "companyName", required = true) String companyName) {
        logger.info("Company search initiated for name: {}", companyName);
        try {
            if (companyName == null || companyName.trim().isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "Company name cannot be empty", null, 
                    List.of(new ApiResponse.ErrorResponse("INVALID_INPUT", "Company name cannot be empty"))
                ));
            }

            List<Map<String, String>> results = companyService.searchCompaniesByName(companyName);
            
            if (results.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(
                    false, 
                    "No companies found matching the search criteria", 
                    null, 
                    List.of(new ApiResponse.ErrorResponse("NO_RECORDS_FOUND", "No companies found matching the search criteria"))
                ));
            }

            return ResponseEntity.ok(new ApiResponse<>(
                true,
                "Companies found",
                results,
                null
            ));

        } catch (Exception e) {
            logger.error("Company search failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
}
}