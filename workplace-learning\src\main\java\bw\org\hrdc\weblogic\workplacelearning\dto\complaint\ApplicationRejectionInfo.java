package bw.org.hrdc.weblogic.workplacelearning.dto.complaint;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DTO containing information about who rejected the original application
 * Used in appeal creation response to provide context about the rejection
 */
@Data
@Builder
public class ApplicationRejectionInfo {
    private String rejectedBy;           // UUID of person who rejected
    private String rejectedByName;       // Name of person who rejected
    private String rejectedByRole;       // Role of person who rejected (OFFICER, MANAGER, etc.)
    private LocalDateTime rejectedAt;    // When the application was rejected
    private String rejectionReason;      // Reason for rejection
    private String applicationStatus;    // Status of original application (REJECTED)
    private boolean notificationSent;    // Whether notification was sent to rejector
}
