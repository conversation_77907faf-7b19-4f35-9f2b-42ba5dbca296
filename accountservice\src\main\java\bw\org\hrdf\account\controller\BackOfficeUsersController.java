package bw.org.hrdf.account.controller;

import bw.org.hrdf.account.dto.BackOfficeProfile;
import bw.org.hrdf.account.entity.UserSettings;
import bw.org.hrdf.account.entity.enums.CommunicationType;
import bw.org.hrdf.account.helper.ApiResponse;
import bw.org.hrdf.account.models.user.SettingsRequest;
import bw.org.hrdf.account.service.BackOfficeUsersService;
import bw.org.hrdf.account.service.UserSettingsService;
import org.keycloak.admin.client.resource.RoleResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

import static bw.org.hrdf.account.helper.ApiResponse.getInternalServerError;

@RestController
@RequestMapping("/api/v1/user/backoffice")
public class BackOfficeUsersController {

    private static final Logger logger = LoggerFactory.getLogger(BackOfficeUsersController.class);

    @Autowired
    private BackOfficeUsersService backOfficeUsersService;

    @Deprecated
    @GetMapping("/users-by-role/{role}")
    public ResponseEntity<?> fetchAllActiveUsers(@PathVariable String role) {
        logger.info("Fetch users of role by: {}", role);
        try {
            List<BackOfficeProfile> userDetails = backOfficeUsersService.retrieveUsersByRoles(role);
            if(userDetails.isEmpty()){
                return ApiResponse.createErrorResponse("USER_NOT_FOUND", "Users not found");
            } else {
                return ResponseEntity.ok(new ApiResponse<>(true, "Users reserved successfully", userDetails, null));
            }
        } catch (Exception e) {
            logger.error("Users failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{role}")
    public ResponseEntity<?> fetchAllActiveUsersWithGroups(@PathVariable String role, @RequestParam(value = "group", required = false) String group) {

        logger.info("Fetching users by role: {}, Group: {}", role, (group != null ? group : "N/A"));

        try {
            List<BackOfficeProfile> userDetails = backOfficeUsersService.retrieveUsersByRoleAndGroup(role, group);

            if (userDetails.isEmpty()) {
                return ApiResponse.createErrorResponse("USER_NOT_FOUND", "Users not found");
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "Users retrieved successfully", userDetails, null));

        } catch (Exception e) {
            logger.error("Fetching users failed: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * Search users by partial assignee match
     * @param assignee Partial assignee to search for
     * @return List of matching users with their IDs and userNames
     */
    @GetMapping("/search")
    public ResponseEntity<?> searchUsersByPartialUsername(@RequestParam("assignee") String assignee) {
        logger.info("Searching users with partial assignee match: {}", assignee);
        
        try {
            if (assignee == null || assignee.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(new ApiResponse<>(false, "Assignee search term is required", null, 
                            List.of(new ApiResponse.ErrorResponse("INVALID_REQUEST", "Assignee search term is required"))));
            }
            
            // Search users using the service
            List<UserRepresentation> users = backOfficeUsersService.searchUsersByPartialUsername(assignee);
            
            if (users.isEmpty()) {
                logger.info("No users found matching the search term: {}", assignee);
                return ResponseEntity.ok(new ApiResponse<>(false, "No users found", List.of(), null));
            }
            
            // Map results to response format
            List<Map<String, String>> userDataList = users.stream()
                    .map(user -> {
                        Map<String, String> userData = new HashMap<>();
                        userData.put("userId", user.getId());
                        userData.put("userName", user.getUsername());
                        return userData;
                    })
                    .collect(Collectors.toList());
            
            logger.info("Found {} users matching the search term: {}", users.size(), assignee);
            return ResponseEntity.ok(new ApiResponse<>(true, "Users found", userDataList, null));
            
        } catch (Exception e) {
            logger.error("Error searching users: {}", e.getMessage(), e);
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * Get user details by user ID
     * @param userId The ID of the user to retrieve
     * @return User details including username and roles
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getUserDetailsById(@PathVariable String userId) {
        logger.info("Fetching user details for user ID: {}", userId);
        
        try {
            if (userId == null || userId.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(new ApiResponse<>(false, "User ID is required", null, 
                            List.of(new ApiResponse.ErrorResponse("INVALID_REQUEST", "User ID is required"))));
            }
            
            // Get user representation from Keycloak
            UserRepresentation user = backOfficeUsersService.getUserById(userId);
            
            if (user == null) {
                logger.info("No user found with ID: {}", userId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "User not found", null, 
                            List.of(new ApiResponse.ErrorResponse("USER_NOT_FOUND", "User not found"))));
            }
            
            // Get user roles
            Map<String, List<String>> userRoles = backOfficeUsersService.getUserRoles(userId);
            
            // Create response with user details and roles
            Map<String, Object> userDetails = new HashMap<>();
            userDetails.put("userId", user.getId());
            userDetails.put("username", user.getUsername());
            userDetails.put("firstName", user.getFirstName());
            userDetails.put("lastName", user.getLastName());
            userDetails.put("email", user.getEmail());
            userDetails.put("enabled", user.isEnabled());
            userDetails.put("emailVerified", user.isEmailVerified());
            userDetails.put("roles", userRoles);
            
            logger.info("Successfully retrieved user details for user ID: {}", userId);
            return ResponseEntity.ok(new ApiResponse<>(true, "User details retrieved successfully", userDetails, null));
            
        } catch (Exception e) {
            logger.error("Error retrieving user details: {}", e.getMessage(), e);
            return getInternalServerError(e.getMessage());
        }
    }
}

