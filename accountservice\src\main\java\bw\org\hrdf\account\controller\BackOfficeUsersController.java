package bw.org.hrdf.account.controller;

import bw.org.hrdf.account.dto.BackOfficeProfile;
import bw.org.hrdf.account.entity.UserSettings;
import bw.org.hrdf.account.entity.enums.CommunicationType;
import bw.org.hrdf.account.helper.ApiResponse;
import bw.org.hrdf.account.models.user.SettingsRequest;
import bw.org.hrdf.account.service.BackOfficeUsersService;
import bw.org.hrdf.account.service.UserSettingsService;
import org.keycloak.admin.client.resource.RoleResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static bw.org.hrdf.account.helper.ApiResponse.getInternalServerError;

@RestController
@RequestMapping("/api/v1/user/backoffice")
public class BackOfficeUsersController {

    private static final Logger logger = LoggerFactory.getLogger(BackOfficeUsersController.class);

    @Autowired
    private BackOfficeUsersService backOfficeUsersService;

    @Deprecated
    @GetMapping("/users-by-role/{role}")
    public ResponseEntity<?> fetchAllActiveUsers(@PathVariable String role) {
        logger.info("Fetch users of role by: {}", role);
        try {
            List<BackOfficeProfile> userDetails = backOfficeUsersService.retrieveUsersByRoles(role);
            if(userDetails.isEmpty()){
                return ApiResponse.createErrorResponse("USER_NOT_FOUND", "Users not found");
            } else {
                return ResponseEntity.ok(new ApiResponse<>(true, "Users reserved successfully", userDetails, null));
            }
        } catch (Exception e) {
            logger.error("Users failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{role}")
    public ResponseEntity<?> fetchAllActiveUsersWithGroups(@PathVariable String role, @RequestParam(value = "group", required = false) String group) {

        logger.info("Fetching users by role: {}, Group: {}", role, (group != null ? group : "N/A"));

        try {
            List<BackOfficeProfile> userDetails = backOfficeUsersService.retrieveUsersByRoleAndGroup(role, group);

            if (userDetails.isEmpty()) {
                return ApiResponse.createErrorResponse("USER_NOT_FOUND", "Users not found");
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "Users retrieved successfully", userDetails, null));

        } catch (Exception e) {
            logger.error("Fetching users failed: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * ✅ CLIENT FEEDBACK: Agent lead is a user, use existing function to get user by id
     * Get user details by user ID
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> fetchUserById(@PathVariable String userId) {
        logger.info("Fetching user by ID: {}", userId);
        try {
            BackOfficeProfile userDetails = backOfficeUsersService.retrieveUserById(userId);

            if (userDetails == null) {
                return ApiResponse.createErrorResponse("USER_NOT_FOUND", "User not found");
            }

            return ResponseEntity.ok(new ApiResponse<>(true, "User retrieved successfully", userDetails, null));

        } catch (Exception e) {
            logger.error("Fetching user failed: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }
}

