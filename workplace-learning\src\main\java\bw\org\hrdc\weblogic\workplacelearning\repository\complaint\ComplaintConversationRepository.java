package bw.org.hrdc.weblogic.workplacelearning.repository.complaint;

import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintConversation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * Repository for ComplaintConversation entity
 * 
 * <AUTHOR>
 * @CreatedOn 17/06/25 15:35
 */
@Repository
public interface ComplaintConversationRepository extends JpaRepository<ComplaintConversation, UUID> {

    /**
     * Find all conversations for a specific complaint, ordered by creation time
     */
    @Query("SELECT cc FROM ComplaintConversation cc WHERE cc.complaintId = :complaintId AND cc.deleted = false ORDER BY cc.createdAt ASC")
    List<ComplaintConversation> findByComplaintIdOrderByCreatedAtAsc(@Param("complaintId") String complaintId);

    /**
     * Find all conversations for a specific complaint with pagination
     */
    @Query("SELECT cc FROM ComplaintConversation cc WHERE cc.complaintId = :complaintId AND cc.deleted = false ORDER BY cc.createdAt ASC")
    Page<ComplaintConversation> findByComplaintIdOrderByCreatedAtAsc(@Param("complaintId") String complaintId, Pageable pageable);

    /**
     * Find unread messages for a specific user
     */
    @Query("SELECT cc FROM ComplaintConversation cc WHERE cc.toUser = :userId AND cc.isRead = false AND cc.deleted = false ORDER BY cc.createdAt DESC")
    List<ComplaintConversation> findUnreadMessagesByUser(@Param("userId") String userId);

    /**
     * Count unread messages for a specific user
     */
    @Query("SELECT COUNT(cc) FROM ComplaintConversation cc WHERE cc.toUser = :userId AND cc.isRead = false AND cc.deleted = false")
    Long countUnreadMessagesByUser(@Param("userId") String userId);

    /**
     * Find latest message for a complaint
     */
    @Query("SELECT cc FROM ComplaintConversation cc WHERE cc.complaintId = :complaintId AND cc.deleted = false ORDER BY cc.createdAt DESC LIMIT 1")
    ComplaintConversation findLatestMessageByComplaintId(@Param("complaintId") String complaintId);

    /**
     * Mark messages as read
     */
    @Query("UPDATE ComplaintConversation cc SET cc.isRead = true, cc.updatedAt = CURRENT_TIMESTAMP WHERE cc.complaintId = :complaintId AND cc.toUser = :userId AND cc.isRead = false")
    void markMessagesAsRead(@Param("complaintId") String complaintId, @Param("userId") String userId);

    /**
     * Find conversations between two users for a specific complaint
     */
    @Query("SELECT cc FROM ComplaintConversation cc WHERE cc.complaintId = :complaintId AND " +
           "((cc.fromUser = :user1 AND cc.toUser = :user2) OR (cc.fromUser = :user2 AND cc.toUser = :user1)) " +
           "AND cc.deleted = false ORDER BY cc.createdAt ASC")
    List<ComplaintConversation> findConversationBetweenUsers(@Param("complaintId") String complaintId, 
                                                            @Param("user1") String user1, 
                                                            @Param("user2") String user2);
}
