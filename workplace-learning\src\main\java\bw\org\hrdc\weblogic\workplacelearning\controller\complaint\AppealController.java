package bw.org.hrdc.weblogic.workplacelearning.controller.complaint;

import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.*;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintDocument;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.service.complaint.AppealService;
import bw.org.hrdc.weblogic.workplacelearning.service.complaint.PaperlessService;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.inject.Inject;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse.getInternalServerError;

/**
 * <AUTHOR>
 * @CreatedOn 07/04/25 03:09
 * @UpdatedBy martinspectre
 * @UpdatedOn 07/04/25 03:09
 */
@RestController
@RequestMapping("/api/v1/appeals")
@RequiredArgsConstructor
public class AppealController {
    private static final Logger logger = LoggerFactory.getLogger(AppealController.class);

    @Inject
    private final AppealService appealService;
    @Inject
    private final PaperlessService paperlessService;

    @PostMapping
    public ResponseEntity<?> createAppeal(@RequestBody() RequestPayload payload, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Appeal creation initiated by: {}", userId);
        try {
            // ✅ Use enhanced appeal creation with rejector information
            AppealCreationResponse appealResponse = appealService.createAppealWithRejectorInfo(payload.getDetails(), userId);

            if(appealResponse != null){
                // Handle document attachments
                if (payload.getDocuments() != null) {
                    List<ComplaintDocument> documents = payload.getDocuments();
                    for (ComplaintDocument document : documents) {
                        ComplaintEntity savedAppeal = appealService.fetchById(appealResponse.getUuid());
                        document.setComplaint(savedAppeal);
                        appealService.createAppealDocument(document);
                    }
                }

                //TODO Send an email to client contact about the created appeal with reference time and resolution time
                return ResponseEntity.ok(new ApiResponse<>(true, "Appeal created successfully and rejector notified", appealResponse, null));
            }else {
                logger.error("Appeal creation failed");
                return ApiResponse.createErrorResponse("APPEAL_FILING_ERROR", "Failed to create appeal.");
            }
        } catch (Exception e) {
            logger.error("Appeal creation failed with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> getAllComplaints(@PathVariable Integer offset, @PathVariable Integer pageSize) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Page<ListData> applications = appealService.fetchList(PageRequest.of(offset, pageSize));

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<?>> getComplaintById(@PathVariable String id) {
        try {
            ComplaintResponse complaint = appealService.fetchDetailedAppeal(id);
            if(complaint != null){
                return ResponseEntity.ok(new ApiResponse<>(true, "Record found", complaint, null));

            }else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "No record found", null, null));

            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<?> updateStatus(@PathVariable String id, @RequestBody ComplaintStatusUpdatePayload request, @RequestHeader("X-Authenticated-User") String userId) {

        try {
            ComplaintEntity complaint = appealService.updateStatus(id, userId, request.getStatus(), request.getState());
            if(complaint != null){
                //TODO send email if the complain is closed or escalated
                return ResponseEntity.ok(new ApiResponse<>(true, "Appeal updated", complaint, null));

            }else {
                return ResponseEntity.status(HttpStatus.OK)
                        .body(new ApiResponse<>(false, "Error updating appeal status", null, null));

            }
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{offset}/{pageSize}/{companyId}")
    public ResponseEntity<ApiResponse<?>> getAllCompanyAppealss(@PathVariable Integer offset, @PathVariable Integer pageSize, @PathVariable String companyId) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Sort sort = Sort.by("status").descending();
            Pageable pageable = PageRequest.of(offset, pageSize, sort);

            Page<ListData> applications = appealService.fetchCompanyList(companyId, pageable);

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/{companyId}/statistics")
    public ResponseEntity<?> getCompanyStatistics(@PathVariable String companyId) {
        try {
            ComplaintsStats statistics =  appealService.getCompanyStatistics(companyId);
            return ResponseEntity.ok(new ApiResponse<>(true, "Statistical data", statistics, null));
        } catch (Exception e) {
            logger.error("Failed to fetch company statistical data with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @GetMapping("/statistics")
    public ResponseEntity<?> getAllStatistics() {
        try {
            ComplaintsStats statistics =  appealService.getStatistics();
            return ResponseEntity.ok(new ApiResponse<>(true, "Statistical data", statistics, null));
        } catch (Exception e) {
            logger.error("Failed to fetch statistical data with exception: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    @PostMapping("/{offset}/{pageSize}")
    public ResponseEntity<ApiResponse<?>> getAllUserAssignedComplaints(@PathVariable Integer offset, @PathVariable Integer pageSize, @RequestBody ComplaintSearchCriteria searchCriteria) {
        try {
            if (offset == null)
                offset = 0;
            if(pageSize <= 0) {
                pageSize = 10;
            }

            Page<ListData> applications = appealService.fetchUserAssignedList(PageRequest.of(offset, pageSize), searchCriteria);

            if (applications.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(false, "No records found", null, null));
            }

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("currentPage", applications.getNumber());
            metadata.put("totalPages", applications.getTotalPages());
            metadata.put("totalElements", applications.getTotalElements());
            metadata.put("pageSize", applications.getSize());

            Map<String, Object> response = new HashMap<>();
            response.put("metadata", metadata);
            response.put("results", applications.getContent());

            return ResponseEntity.ok(new ApiResponse<>(true, "Records found", response, null));
        } catch (Exception e) {
            return getInternalServerError(e.getMessage());
        }
    }

    @PatchMapping("/{id}/assign-agent/{agentId}")
    public ResponseEntity<?> assignAgent(@PathVariable String id, @PathVariable String agentId, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Appeal agent assignment initiated for complaint id : {}", id);
        try {
            ComplaintEntity complaint = appealService.fetchById(id);
            if(complaint != null){
                complaint.setAssignee(agentId);
                ComplaintEntity updateStatus = appealService.assignedNewAssistance(id, agentId, userId);
                if (updateStatus == null) {
                    logger.error("Failed to assign appeal {} to agent", id);
                    return ApiResponse.createErrorResponse("APPEAL_UPDATE_ERROR", "Failed to process appeal action.");
                }else{
                    //TODO trigger notification to agent about the reassignment
                    //TODO trigger notification to agent for the assignment made

                    return ResponseEntity.ok(new ApiResponse<>(true, "Agent assigned successfully", null, null));
                }
            }else{
                return ApiResponse.createErrorResponse("APPEAL_NOT_FOUND", "Provided appeal id does not exist");
            }
        } catch (Exception e) {
            logger.error("Failed to assign appeal id {} to agent id {} with exception: {}", id, agentId, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * ✅ ADD: Manager communicates with client (Same as complaint module)
     */
    @PatchMapping("/{id}/communicate")
    public ResponseEntity<?> communicateWithClient(@PathVariable String id, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Manager communication initiated for appeal id: {}", id);
        try {
            ComplaintEntity appeal = appealService.fetchById(id);
            if (appeal != null) {
                // Manager communication always uses UNDER_REVIEW state
                ComplaintEntity updatedAppeal = appealService.updateStatus(id, userId,
                    Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK, Enums.ComplaintState.UNDER_REVIEW);

                return ResponseEntity.ok(new ApiResponse<>(true, "Communication initiated with client", updatedAppeal, null));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "Appeal not found", null, null));
            }
        } catch (Exception e) {
            logger.error("Failed to initiate communication for appeal id {} with exception: {}", id, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * ✅ ADD: Manager closes appeal (Same as complaint module)
     */
    @PatchMapping("/{id}/close")
    public ResponseEntity<?> closeAppeal(@PathVariable String id, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Manager close appeal initiated for appeal id: {}", id);
        try {
            ComplaintEntity appeal = appealService.fetchById(id);
            if (appeal != null) {
                ComplaintEntity updatedAppeal = appealService.updateStatus(id, userId,
                    Enums.ComplaintStatus.CLOSED, Enums.ComplaintState.COMPLETED);

                // ✅ ADD: Send notification to client about appeal closure
                try {
                    // TODO: Implement notification to client about appeal closure
                    logger.info("Notification should be sent to client about appeal closure: {}", id);
                } catch (Exception e) {
                    logger.warn("Failed to send notification to client: {}", e.getMessage());
                }

                return ResponseEntity.ok(new ApiResponse<>(true, "Appeal closed successfully", updatedAppeal, null));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "Appeal not found", null, null));
            }
        } catch (Exception e) {
            logger.error("Failed to close appeal id {} with exception: {}", id, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * ✅ ADD: Client responds to Manager (Same as complaint module)
     */
    @PatchMapping("/{id}/client-response")
    public ResponseEntity<?> clientResponse(@PathVariable String id, @RequestHeader("X-Authenticated-User") String userId) {
        logger.info("Client response recorded for appeal id: {}", id);
        try {
            ComplaintEntity appeal = appealService.fetchById(id);
            if (appeal != null) {
                // For appeals, we always use UNDER_REVIEW state since there's no escalation
                ComplaintEntity updatedAppeal = appealService.updateStatus(id, userId,
                    Enums.ComplaintStatus.IN_PROGRESS, Enums.ComplaintState.UNDER_REVIEW);

                return ResponseEntity.ok(new ApiResponse<>(true, "Client response recorded", updatedAppeal, null));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new ApiResponse<>(false, "Appeal not found", null, null));
            }
        } catch (Exception e) {
            logger.error("Failed to record client response for appeal id {} with exception: {}", id, e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }

    /**
     * ✅ ADD: Search appeals with filters (Same as complaint module)
     */
    @GetMapping("/search")
    public ResponseEntity<?> searchAppeals(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String referenceNumber,
            @RequestParam(required = false) String department,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String assignedTo,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestHeader("X-Authenticated-User") String userId) {

        try {
            ComplaintSearchCriteria searchCriteria = new ComplaintSearchCriteria();
            searchCriteria.setStatus(status);
            searchCriteria.setState(state);
            searchCriteria.setReferenceNumber(referenceNumber);
            searchCriteria.setDepartment(department);
            searchCriteria.setRole(role);
            searchCriteria.setAssignedTo(assignedTo);
            searchCriteria.setStartDate(startDate);
            searchCriteria.setEndDate(endDate);

            PageRequest pageable = PageRequest.of(page, size);
            Page<ListData> appeals = appealService.fetchUserAssignedList(pageable, searchCriteria);

            return ResponseEntity.ok(new ApiResponse<>(true, "Appeals retrieved successfully", appeals, null));
        } catch (Exception e) {
            logger.error("Failed to search appeals: {}", e.getMessage());
            return getInternalServerError(e.getMessage());
        }
    }
}
