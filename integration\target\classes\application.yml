server:
  port: 3550

spring:
  application:
    name: integration-service
  profiles:
    active: ${PROFILE_ACTIVE:local}
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
#    password: ${REDIS_PASSWORD:admin}  # Optional: if Red<PERSON> requires authentication
    time-to-live: 600000 # 10 minutes (in milliseconds)
    jedis:
      pool:
        max-active: 10  # Maximum number of connections
        max-idle: 5     # Maximum idle connections
        min-idle: 1     # Minimum idle connections
        max-wait: -1ms  # Maximum wait time for obtaining a connection
  cache:
    type: redis

eureka:
  instance:
    preferIpAddress: true
    leaseRenewalIntervalInSeconds: 5   # Reduce interval to avoid expiration issues
    leaseExpirationDurationInSeconds: 10 # Reduce expiration timeout
  client:
    fetchRegistry: true
    registerWithEureka: true
    serviceUrl:
      defaultZone: ${EUREKA_DEFAULT_ZONE:http://localhost:8070/eureka/}
    healthcheck:
      enabled: true

api:
  external:
    omang:
      url: ${OMANG_ENDPOINT:https://1gov-dev-gateway.gov.bw:8443/apiman-gateway/Omang/EID/1.0}
      key: ${OMANG_APIKEY:KLBzNwGVZMRF9c65MOMV7Cl8rhIMTu4z}
    cipa:
      url: ${CIPA_ENDPOINT:https://1gov-dev-gateway.gov.bw:8443/apiman-gateway/companies-endpoint/2.0/EID/eid-svc}
      key: ${CIPA_APIKEY:add841e7-b032-4441-8745-de10368f2835}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,loggers
  endpoint:
    health:
      show-details: always

# Logging Configuration
logging:
  level:
    org.springframework.security: INFO
    web: INFO
    org:
      hibernate:
        SQL=DEBUG: DEBUG
# Paperless-ngx API configuration
paperless:
  url: "http://**************:8000"
  apiToken: "9b222beb989bdb2cbf920a5d60cc4e8c1d93edf4"