databaseChangeLog:
  - changeSet:
      id: 20250414-1-create-notification-table
      author: <PERSON><PERSON><PERSON>
      changes:
        - createTable:
            tableName: notification
            columns:
              - column:
                  name: id
                  type: UUID
                  constraints:
                    primaryKey: true
                    unique: true
                    nullable: false
              - column:
                  name: name
                  type: VA<PERSON><PERSON><PERSON>(255)
              - column:
                  name: recipient
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: subject
                  type: TEXT
                  constraints:
                    nullable: false
              - column:
                  name: message
                  type: TEXT
                  constraints:
                    nullable: false
              - column:
                  name: sent_at
                  type: TIMESTAMP
              - column:
                  name: type
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
              - column:
                  name: status
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
              - column:
                  name: create_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMESTAMP
              - column:
                  name: create_by
                  type: VA<PERSON><PERSON>R(255)
              - column:
                  name: updated_at
                  type: TIMESTAMP
              - column:
                  name: updated_by
                  type: VARCHA<PERSON>(255)