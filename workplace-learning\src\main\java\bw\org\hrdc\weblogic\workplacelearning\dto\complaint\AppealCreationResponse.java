package bw.org.hrdc.weblogic.workplacelearning.dto.complaint;

import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Enhanced response DTO for appeal creation that includes rejector information
 */
@Data
public class AppealCreationResponse {
    private String uuid;
    private String referenceNumber;
    private String reference;
    private Enums.Department department;
    private String typeOfComplaint;
    private Enums.ComplaintStatus status;
    private Enums.ComplaintState state;
    private String assignee;
    private LocalDateTime createdAt;
    private String organisationId;
    private String rejectedBy;  // Basic rejector ID stored in entity
    
    //  NEW: Detailed original application rejection information
    private ApplicationRejectionInfo originalApplication;
}
