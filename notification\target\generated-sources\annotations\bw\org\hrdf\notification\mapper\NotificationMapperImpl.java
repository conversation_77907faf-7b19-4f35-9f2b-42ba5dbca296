package bw.org.hrdf.notification.mapper;

import bw.org.hrdf.notification.dto.NotificationDTO;
import bw.org.hrdf.notification.model.Notification;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-18T11:09:14+0530",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class NotificationMapperImpl implements NotificationMapper {

    @Override
    public Notification mapToNotification(NotificationDTO notificationDTO) {
        if ( notificationDTO == null ) {
            return null;
        }

        Notification notification = new Notification();

        notification.setId( notificationDTO.getId() );
        notification.setMessage( notificationDTO.getMessage() );
        notification.setName( notificationDTO.getName() );
        notification.setRecipient( notificationDTO.getRecipient() );
        notification.setSentAt( notificationDTO.getSentAt() );
        notification.setStatus( notificationDTO.getStatus() );
        notification.setSubject( notificationDTO.getSubject() );
        notification.setType( notificationDTO.getType() );

        return notification;
    }

    @Override
    public NotificationDTO mapToNotificationDTO(Notification notification) {
        if ( notification == null ) {
            return null;
        }

        NotificationDTO notificationDTO = new NotificationDTO();

        notificationDTO.setId( notification.getId() );
        notificationDTO.setMessage( notification.getMessage() );
        notificationDTO.setName( notification.getName() );
        notificationDTO.setRecipient( notification.getRecipient() );
        notificationDTO.setSentAt( notification.getSentAt() );
        notificationDTO.setStatus( notification.getStatus() );
        notificationDTO.setSubject( notification.getSubject() );
        notificationDTO.setType( notification.getType() );

        return notificationDTO;
    }
}
