package bw.org.hrdc.weblogic.workplacelearning.service.complaint;

import bw.org.hrdc.weblogic.workplacelearning.api.CompanyClient;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ApplicationRejectionInfo;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.AppealCreationResponse;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintResponse;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintSearchCriteria;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ComplaintsStats;
import bw.org.hrdc.weblogic.workplacelearning.dto.complaint.ListData;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.AuditLog;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintDocument;
import bw.org.hrdc.weblogic.workplacelearning.entity.complaint.ComplaintEntity;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.AuditLogRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintDocumentRepository;
import bw.org.hrdc.weblogic.workplacelearning.repository.complaint.ComplaintRepository;
import bw.org.hrdc.weblogic.workplacelearning.util.ApiResponse;
import bw.org.hrdc.weblogic.workplacelearning.util.BaseSpecifications;
import bw.org.hrdc.weblogic.workplacelearning.util.Enums;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import jakarta.persistence.criteria.Predicate;
import java.util.Optional;

/**
 * <AUTHOR>
 * @CreatedOn 07/04/25 02:17
 * @UpdatedBy martinspectre
 * @UpdatedOn 07/04/25 02:17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AppealService {
    private static final Logger logger = LoggerFactory.getLogger(AppealService.class);

    private final ComplaintRepository appealRepository;
    private final AuditLogRepository auditLogRepository;

    private final PaperlessService paperlessService;
    private final ComplaintDocumentRepository documentRepository;
    @Autowired
    private CompanyClient companyClient;

    private String generateAppealReference() {
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String uniquePart = String.format("%04d", (int) (Math.random() * 10000));
        return "APP-" + datePart + "-" + uniquePart;
    }

    /**
     *  ADD: Manager assignment with load balancing (Same pattern as complaint agent assignment)
     */
    private String assignManagerByWorkload() {
        try {
            logger.info("Attempting to fetch managers from Account Service for load balancing...");

            ApiResponse<?> response = companyClient.fetchAllActiveUsers("manager");
            logger.info("Account Service response - Status: {}, Data: {}", response.isStatus(), response.getData());

            if (response.isStatus() && response.getData() != null) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> managerData = (List<Map<String, Object>>) response.getData();
                logger.info("Found {} managers from Account Service", managerData.size());

                if (!managerData.isEmpty()) {
                    String selectedManagerId = findManagerWithLeastWorkload(managerData);
                    logger.info("Assigned appeal to manager with least workload: {}", selectedManagerId);
                    return selectedManagerId;
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to fetch managers from Account Service: {}", e.getMessage());
        }

        throw new RuntimeException("No managers available for appeal assignment. Please ensure Account Service is running and has active managers.");
    }

    /**
     *  ADD: Manager workload calculation (Same pattern as complaint agent workload)
     */
    private String findManagerWithLeastWorkload(List<Map<String, Object>> managers) {
        // Get current appeal workload from database using aggregation query
        List<Object[]> workloadData = appealRepository.getAppealCountByManager();

        // Convert to Map for easy lookup: managerId -> appeal count
        Map<String, Long> workloadMap = workloadData.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],  // assignee (manager ID)
                row -> ((Number) row[1]).longValue()  // appeal count
            ));

        logger.info("Current manager workloads: {}", workloadMap);

        // Find manager with minimum workload (including managers with 0 appeals)
        String selectedManager = managers.stream()
            .map(manager -> (String) manager.get("id"))
            .min(Comparator.comparing(managerId -> workloadMap.getOrDefault(managerId, 0L)))
            .orElse(null);

        Long selectedManagerWorkload = workloadMap.getOrDefault(selectedManager, 0L);
        logger.info("Selected manager {} with {} active appeals", selectedManager, selectedManagerWorkload);

        return selectedManager;
    }

    public ComplaintEntity createAppeal(ComplaintEntity appeal, String userId) {

        String referenceNumber = generateAppealReference();

        //  ADD: Automatic manager assignment with load balancing
        String assignedManagerId = assignManagerByWorkload();

        appeal.setStatus(Enums.ComplaintStatus.OPEN);
        appeal.setState(Enums.ComplaintState.SUBMITTED);
        appeal.setReferenceNumber(referenceNumber);
        appeal.setAssignee(assignedManagerId);
        appeal.setCategory(Enums.CategoryComplaint.APPEAL);

        ComplaintEntity savedAppeal = appealRepository.save(appeal);

        //  ADD: Notify the person who rejected the original application
        try {
            notifyApplicationRejector(appeal, userId);
        } catch (Exception e) {
            logger.warn("Failed to notify application rejector: {}", e.getMessage());
        }

        AuditLog log = new AuditLog(savedAppeal, "CREATED", "Appeal submitted and assigned to manager", userId, LocalDateTime.now());
        auditLogRepository.save(log);

        return savedAppeal;
    }

    /**
     *  NEW: Enhanced appeal creation with rejector information
     */
    public AppealCreationResponse createAppealWithRejectorInfo(ComplaintEntity appeal, String userId) {
        String referenceNumber = generateAppealReference();

        //  Lookup rejector information before saving
        ApplicationRejectionInfo rejectionInfo = findDetailedApplicationRejector(appeal.getReference());

        //  Store basic rejector info in entity
        if (rejectionInfo != null) {
            appeal.setRejectedBy(rejectionInfo.getRejectedBy());
            appeal.setRejectedAt(rejectionInfo.getRejectedAt());
            appeal.setRejectionReason(rejectionInfo.getRejectionReason());
        }

        //  Automatic manager assignment with load balancing
        String assignedManagerId = assignManagerByWorkload();

        appeal.setStatus(Enums.ComplaintStatus.OPEN);
        appeal.setState(Enums.ComplaintState.SUBMITTED);
        appeal.setReferenceNumber(referenceNumber);
        appeal.setAssignee(assignedManagerId);
        appeal.setCategory(Enums.CategoryComplaint.APPEAL);

        ComplaintEntity savedAppeal = appealRepository.save(appeal);

        //  Notify the person who rejected the original application
        boolean notificationSent = false;
        try {
            notifyApplicationRejector(appeal, userId);
            notificationSent = true;
        } catch (Exception e) {
            logger.warn("Failed to notify application rejector: {}", e.getMessage());
        }

        //  Update notification status in rejection info
        if (rejectionInfo != null) {
            rejectionInfo.setNotificationSent(notificationSent);
        }

        AuditLog log = new AuditLog(savedAppeal, "CREATED", "Appeal submitted and assigned to manager", userId, LocalDateTime.now());
        auditLogRepository.save(log);

        //  Build enhanced response
        return buildAppealCreationResponse(savedAppeal, rejectionInfo);
    }

    public ComplaintEntity fetchById(String appealId) {
        return appealRepository.findByUuid(appealId)
                .orElseThrow(() -> new RuntimeException("Appeal not found"));
    }

    public ComplaintResponse fetchDetailedAppeal(String appealId){
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }

        Optional<ComplaintEntity> appeal = appealRepository.findByUuid(appealId);

        if(appeal.isPresent()){

            ComplaintEntity appealData = appeal.get();
            ComplaintResponse appealResponse = new ComplaintResponse();

            appealResponse.setUuid(appealData.getUuid());
            appealResponse.setReference(appealData.getReference());
            appealResponse.setReferenceNumber(appealData.getReferenceNumber());
            appealResponse.setOrganisationId(appealData.getOrganisationId());
            appealResponse.setDepartment(appealData.getDepartment());
            appealResponse.setTypeOfComplaint(appealData.getTypeOfComplaint());
            appealResponse.setState(appealData.getState());
            appealResponse.setStatus(appealData.getStatus());
            appealResponse.setCreatedAt(appealData.getCreatedAt());
            appealResponse.setUpdatedAt(appealData.getUpdatedAt());
            appealResponse.setCreatedBy(appealData.getCreatedBy());
            appealResponse.setUpdatedBy(appealData.getUpdatedBy());
            appealResponse.setAssignedTo(appealData.getAssignee());
            appealResponse.setDocuments(appealData.getDocuments());
            appealResponse.setComments(appealData.getComments());
            appealResponse.setAuditLogs(appealData.getAuditLogs());
            appealResponse.setDescription(appealData.getDescription());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(appealData.getOrganisationId())).toList();

            if(!company.isEmpty()){
                appealResponse.setOrganisation(company.get(0).get("name").toString());
            }

            return appealResponse;
        }
        return null;
    }

    public Page<ListData> fetchList(PageRequest pageable) {
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }

        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isAppeal());

        // Create a new PageRequest with sorting by createdAt in descending order (most recent first)
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        PageRequest sortedPageable = PageRequest.of(
            pageable.getPageNumber(), 
            pageable.getPageSize(), 
            sort
        );

        Page<ComplaintEntity> appeals = appealRepository.findAll(spec, sortedPageable);
        return appeals.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment().name());
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState().name());
            model.setStatus(app.getStatus().name());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintEntity updateStatus(String appealId, String userId, Enums.ComplaintStatus status, Enums.ComplaintState state) {
        ComplaintEntity appeal = fetchById(appealId);
        if(appeal != null){
            // Validate transition
            validateWorkflowTransition(appeal.getStatus(), appeal.getState(), status, state);

            appeal.setStatus(status);
            appeal.setState(state);
            appealRepository.save(appeal);

            AuditLog log = new AuditLog(appeal, "STATUS_CHANGED", "Status changed to " + status, userId, LocalDateTime.now());
            auditLogRepository.save(log);
        }


        return appeal;
    }

    @Transactional()
    public void createAppealDocument(ComplaintDocument appealDocument) {
        documentRepository.save(appealDocument);
    }

    private void validateWorkflowTransition(Enums.ComplaintStatus currentStatus, Enums.ComplaintState currentState,
                                            Enums.ComplaintStatus newStatus, Enums.ComplaintState newState) {

        if (currentStatus == Enums.ComplaintStatus.OPEN && currentState == Enums.ComplaintState.SUBMITTED) {
            if (newStatus == Enums.ComplaintStatus.IN_PROGRESS && newState == Enums.ComplaintState.UNDER_REVIEW) return;
           
            if (newStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && newState == Enums.ComplaintState.UNDER_REVIEW) return;
            
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return;
        }

        
        if (currentStatus == Enums.ComplaintStatus.IN_PROGRESS && currentState == Enums.ComplaintState.UNDER_REVIEW) {
            if (newStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && newState == Enums.ComplaintState.UNDER_REVIEW) return;
           
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return;
        }

       
        if (currentStatus == Enums.ComplaintStatus.AWAITING_CLIENT_FEEDBACK && currentState == Enums.ComplaintState.UNDER_REVIEW) {
            if (newStatus == Enums.ComplaintStatus.IN_PROGRESS && newState == Enums.ComplaintState.UNDER_REVIEW) return;
            
            if (newStatus == Enums.ComplaintStatus.CLOSED && newState == Enums.ComplaintState.COMPLETED) return;
        }

        throw new IllegalArgumentException("Invalid status/state transition from " + currentStatus + "/" + currentState + " to " + newStatus + "/" + newState);
    }

    public Page<ListData> fetchCompanyList(String companyId, Pageable pageable) {
        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }
        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isAppeal());
        spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("organisationId"), companyId));

        // Create a new PageRequest with sorting by createdAt in descending order (most recent first)
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        PageRequest sortedPageable = PageRequest.of(
            pageable.getPageNumber(), 
            pageable.getPageSize(), 
            sort
        );

        Page<ComplaintEntity> appeals =  appealRepository.findAll(spec, sortedPageable);
        return appeals.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment().name());
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState().name());
            model.setStatus(app.getStatus().name());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintsStats getCompanyStatistics(String companyId) {
        List<Object[]> results = appealRepository.getCompanyComplaintStatistics(companyId, Enums.CategoryComplaint.APPEAL.name());
        if (!results.isEmpty()) {
            Object[] row = results.get(0);
            return new ComplaintsStats(
                    ((Number) row[0]).longValue(),
                    ((Number) row[1]).longValue(),
                    ((Number) row[2]).longValue(),
                    ((Number) row[3]).longValue(),
                    ((Number) row[4]).longValue()
            );
        }
        return new ComplaintsStats(0L, 0L, 0L, 0L, 0L);
    }

    public ComplaintsStats getStatistics() {
        List<Object[]> results = appealRepository.getComplaintStatistics(Enums.CategoryComplaint.APPEAL.name());
        if (!results.isEmpty()) {
            Object[] row = results.get(0);
            return new ComplaintsStats(
                    ((Number) row[0]).longValue(),
                    ((Number) row[1]).longValue(),
                    ((Number) row[2]).longValue(),
                    ((Number) row[3]).longValue(),
                    ((Number) row[4]).longValue()
            );
        }
        return new ComplaintsStats(0L, 0L, 0L, 0L, 0L);
    }

    public Page<ListData> fetchUserAssignedList(PageRequest pageable, ComplaintSearchCriteria searchCriteria) {

        ApiResponse<?> apiResponse = companyClient.fetchAllCompanies();

        List<Map<String, Object>> res;

        if (apiResponse.isStatus() && apiResponse.getData()!=null) {
            res = (List<Map<String, Object>>) apiResponse.getData();
        }
        else{
            res = List.of();
        }
        Specification<ComplaintEntity> spec = Specification.where(BaseSpecifications.isNotDeleted());
        spec = spec.and(BaseSpecifications.isAppeal());

        //  ADD: Role-based filtering (Same as complaint module)
        if (searchCriteria.getRole() != null) {
            spec = spec.and(buildAppealRoleBasedSpecification(searchCriteria.getRole(), searchCriteria.getAssignedTo()));
        } else if (searchCriteria.getAssignedTo() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("assignee"), searchCriteria.getAssignedTo()));
        }

        //  ADD: Status filtering
        if (searchCriteria.getStatus() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("status"), Enums.ComplaintStatus.valueOf(searchCriteria.getStatus().toUpperCase())));
        }

        //  ADD: State filtering
        if (searchCriteria.getState() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("state"), Enums.ComplaintState.valueOf(searchCriteria.getState().toUpperCase())));
        }

        //  ADD: Reference number filtering
        if (searchCriteria.getReferenceNumber() != null && !searchCriteria.getReferenceNumber().isEmpty()) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("referenceNumber")),
                    "%" + searchCriteria.getReferenceNumber().toLowerCase() + "%"));
        }

        //  ADD: Department filtering
        if (searchCriteria.getDepartment() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.equal(
                    root.get("department"), Enums.Department.valueOf(searchCriteria.getDepartment().toUpperCase())));
        }

        //  ADD: Date range filtering
        if (searchCriteria.getStartDate() != null && searchCriteria.getEndDate() != null) {
            spec = spec.and((root, query, criteriaBuilder) -> criteriaBuilder.between(
                    root.get("createdAt"), searchCriteria.getStartDate(), searchCriteria.getEndDate()));
        }

        if (searchCriteria.getComplaintStates() != null && !searchCriteria.getComplaintStates().isEmpty()) {
            List<Enums.ComplaintState> states = searchCriteria.getComplaintStates();
            spec = spec.and((root, query, criteriaBuilder) -> root.get("state").in(states));
        }

        Page<ComplaintEntity> appeals = appealRepository.findAll(spec, pageable);
        return appeals.map(app -> {
            ListData model = new ListData();
            model.setUuid(app.getUuid());
            model.setDepartment(app.getDepartment().name());
            model.setTypeOfComplaint(app.getTypeOfComplaint());
            model.setReference(app.getReference());
            model.setReferenceNumber(app.getReferenceNumber());
            model.setCreatedAt(app.getCreatedAt().toString());
            model.setAssignee(app.getAssignee());
            model.setState(app.getState().name());
            model.setStatus(app.getStatus().name());

            List<Map<String, Object>> company = res.stream().filter(companyDTO -> !companyDTO.isEmpty() && companyDTO.get("uuid").toString().equals(app.getOrganisationId())).toList();
            if(!company.isEmpty()){
                model.setComplainant(company.get(0).get("name").toString());
            }
            return model;
        });
    }

    public ComplaintEntity assignedNewAssistance(String appealId, String  agentId, String userId) {
        ComplaintEntity appeal = fetchById(appealId);
        if(appeal != null){
            appeal.setAssignee(agentId);
            appealRepository.save(appeal);

            AuditLog log = new AuditLog(appeal, "ASSIGN_AGENT_TO_COMPLAINT", "Appeal assigned to human agent ", userId, LocalDateTime.now());
            auditLogRepository.save(log);
        }


        return appeal;
    }

    /**
     *  ADD: Notify the person who rejected the original application
     */
    private void notifyApplicationRejector(ComplaintEntity appeal, String userId) {
        try {
            logger.info("Looking up rejector for appeal reference: {}", appeal.getReference());

            // Get the original application details using the reference number
            String applicationRejectorId = findApplicationRejector(appeal.getReference());

            if (applicationRejectorId != null) {
                logger.info("Found application rejector: {}", applicationRejectorId);

                // Send notification to the rejector
                sendAppealNotificationToRejector(applicationRejectorId, appeal, userId);
            } else {
                logger.warn("Could not find rejector for application reference: {}", appeal.getReference());
            }
        } catch (Exception e) {
            logger.error("Error notifying application rejector: {}", e.getMessage());
        }
    }

    /**
     *  ADD: Find who rejected the original application
     */
    private String findApplicationRejector(String applicationReference) {
        try {
            // Check different application services to find the rejector

            // 1. Check Pre-approval Service
            String rejectorId = findRejectorInPreApprovalService(applicationReference);
            if (rejectorId != null) return rejectorId;

            // 2. Check Funding Service
            rejectorId = findRejectorInFundingService(applicationReference);
            if (rejectorId != null) return rejectorId;

            // 3. Check NCBSC Service
            rejectorId = findRejectorInNCBSCService(applicationReference);
            if (rejectorId != null) return rejectorId;

            // 4. Check Workplace Learning Service
            rejectorId = findRejectorInWorkplaceService(applicationReference);
            if (rejectorId != null) return rejectorId;

            return null;
        } catch (Exception e) {
            logger.error("Error finding application rejector: {}", e.getMessage());
            return null;
        }
    }

    /**
     *  NEW: Find detailed rejector information for enhanced response
     */
    private ApplicationRejectionInfo findDetailedApplicationRejector(String applicationReference) {
        try {
            logger.info("Looking up detailed rejector information for reference: {}", applicationReference);

            // Try each service to find detailed rejection information
            ApplicationRejectionInfo rejectionInfo = findDetailedRejectorInPreApprovalService(applicationReference);
            if (rejectionInfo != null) return rejectionInfo;

            rejectionInfo = findDetailedRejectorInFundingService(applicationReference);
            if (rejectionInfo != null) return rejectionInfo;

            rejectionInfo = findDetailedRejectorInNCBSCService(applicationReference);
            if (rejectionInfo != null) return rejectionInfo;

            rejectionInfo = findDetailedRejectorInWorkplaceService(applicationReference);
            if (rejectionInfo != null) return rejectionInfo;

            // If no detailed info found, create a basic one with just the rejector ID
            String rejectorId = findApplicationRejector(applicationReference);
            if (rejectorId != null) {
                return ApplicationRejectionInfo.builder()
                    .rejectedBy(rejectorId)
                    .rejectedByName("Unknown")
                    .rejectedByRole("UNKNOWN")
                    .rejectedAt(LocalDateTime.now().minusDays(1)) // Assume rejected recently
                    .rejectionReason("Rejection reason not available")
                    .applicationStatus("REJECTED")
                    .notificationSent(false)
                    .build();
            }

            return null;
        } catch (Exception e) {
            logger.error("Error finding detailed application rejector: {}", e.getMessage());
            return null;
        }
    }

    /**
     *  ADD: Check each service for the rejector
     */
    private String findRejectorInPreApprovalService(String reference) {
        try {
            // Call Pre-approval Service API to get application details
            // Note: This would need to be implemented with actual API calls
            logger.debug("Checking Pre-approval Service for reference: {}", reference);
            // TODO: Implement actual API call to pre-approval service
            return null;
        } catch (Exception e) {
            logger.debug("Application not found in Pre-approval Service: {}", reference);
            return null;
        }
    }

    private String findRejectorInFundingService(String reference) {
        try {
            logger.debug("Checking Funding Service for reference: {}", reference);
            // TODO: Implement actual API call to funding service
            return null;
        } catch (Exception e) {
            logger.debug("Application not found in Funding Service: {}", reference);
            return null;
        }
    }

    private String findRejectorInNCBSCService(String reference) {
        try {
            logger.debug("Checking NCBSC Service for reference: {}", reference);
            // TODO: Implement actual API call to NCBSC service
            return null;
        } catch (Exception e) {
            logger.debug("Application not found in NCBSC Service: {}", reference);
            return null;
        }
    }

    private String findRejectorInWorkplaceService(String reference) {
        try {
            logger.debug("Checking Workplace Learning Service for reference: {}", reference);
            // TODO: Implement actual API call to workplace learning service
            return null;
        } catch (Exception e) {
            logger.debug("Application not found in Workplace Learning Service: {}", reference);
            return null;
        }
    }

    /**
     *  ADD: Send notification to the rejector
     */
    private void sendAppealNotificationToRejector(String rejectorId, ComplaintEntity appeal, String userId) {
        try {
            // Create notification payload
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("appealId", appeal.getUuid());
            notificationData.put("appealReference", appeal.getReferenceNumber());
            notificationData.put("originalReference", appeal.getReference());
            notificationData.put("department", appeal.getDepartment());
            notificationData.put("appealType", appeal.getTypeOfComplaint());
            notificationData.put("rejectorId", rejectorId);
            notificationData.put("createdBy", userId);

            // Send email notification
            logger.info("Sending appeal notification to rejector: {}", rejectorId);

            // TODO: Integrate with Communication Service
            // communicationService.sendAppealNotification(rejectorId, notificationData);

            logger.info("Appeal notification sent successfully to rejector: {}", rejectorId);
        } catch (Exception e) {
            logger.error("Failed to send appeal notification to rejector {}: {}", rejectorId, e.getMessage());
        }
    }

    /**
     *  NEW: Detailed rejector lookup methods for each service
     */
    private ApplicationRejectionInfo findDetailedRejectorInPreApprovalService(String reference) {
        try {
            logger.debug("Checking Pre-approval Service for detailed rejection info: {}", reference);
            // TODO: Implement actual API call to pre-approval service
            // For now, return null - this would be implemented with actual service calls
            return null;
        } catch (Exception e) {
            logger.debug("Detailed rejection info not found in Pre-approval Service: {}", reference);
            return null;
        }
    }

    private ApplicationRejectionInfo findDetailedRejectorInFundingService(String reference) {
        try {
            logger.debug("Checking Funding Service for detailed rejection info: {}", reference);
            // TODO: Implement actual API call to funding service
            return null;
        } catch (Exception e) {
            logger.debug("Detailed rejection info not found in Funding Service: {}", reference);
            return null;
        }
    }

    private ApplicationRejectionInfo findDetailedRejectorInNCBSCService(String reference) {
        try {
            logger.debug("Checking NCBSC Service for detailed rejection info: {}", reference);
            // TODO: Implement actual API call to NCBSC service
            return null;
        } catch (Exception e) {
            logger.debug("Detailed rejection info not found in NCBSC Service: {}", reference);
            return null;
        }
    }

    private ApplicationRejectionInfo findDetailedRejectorInWorkplaceService(String reference) {
        try {
            logger.debug("Checking Workplace Learning Service for detailed rejection info: {}", reference);
            // TODO: Implement actual API call to workplace learning service
            return null;
        } catch (Exception e) {
            logger.debug("Detailed rejection info not found in Workplace Learning Service: {}", reference);
            return null;
        }
    }

    /**
     *  NEW: Build enhanced appeal creation response
     */
    private AppealCreationResponse buildAppealCreationResponse(ComplaintEntity savedAppeal, ApplicationRejectionInfo rejectionInfo) {
        AppealCreationResponse response = new AppealCreationResponse();

        // Basic appeal information
        response.setUuid(savedAppeal.getUuid());
        response.setReferenceNumber(savedAppeal.getReferenceNumber());
        response.setReference(savedAppeal.getReference());
        response.setDepartment(savedAppeal.getDepartment());
        response.setTypeOfComplaint(savedAppeal.getTypeOfComplaint());
        response.setStatus(savedAppeal.getStatus());
        response.setState(savedAppeal.getState());
        response.setAssignee(savedAppeal.getAssignee());
        response.setCreatedAt(savedAppeal.getCreatedAt());
        response.setOrganisationId(savedAppeal.getOrganisationId());
        response.setRejectedBy(savedAppeal.getRejectedBy());

        // Enhanced rejection information
        response.setOriginalApplication(rejectionInfo);

        return response;
    }

    /**
     *  FIXED: Role-based filtering for appeals - Only managers should handle appeals
     */
    private Specification<ComplaintEntity> buildAppealRoleBasedSpecification(String role, String userId) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            logger.info("Building appeal role-based specification for role: {} and userId: {}", role, userId);

            switch (role.toUpperCase()) {
                case "MANAGER":
                    
                    if (userId != null && !userId.isEmpty()) {
                        predicates.add(criteriaBuilder.equal(root.get("assignee"), userId));
                        logger.info("MANAGER filter (with userId): assignee={}", userId);
                    } else {
                        // If no userId provided, managers see all appeals
                        logger.info("MANAGER filter (without userId): all appeals");
                    }
                    break;

                case "AGENT_LEAD":
                    
                    predicates.add(criteriaBuilder.equal(root.get("uuid"), "NO_MATCH"));
                    logger.info("AGENT_LEAD filter: No access to appeals");
                    break;

                case "AGENT":
                    
                    predicates.add(criteriaBuilder.equal(root.get("uuid"), "NO_MATCH"));
                    logger.info("AGENT filter: No access to appeals");
                    break;

                default:
                    
                    predicates.add(criteriaBuilder.equal(root.get("uuid"), "NO_MATCH"));
                    logger.info("Unknown role filter: No access");
            }

            return predicates.isEmpty() ? criteriaBuilder.conjunction() :
                   criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

}
